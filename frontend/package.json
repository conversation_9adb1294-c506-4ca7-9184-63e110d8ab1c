{"name": "frontend", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.0", "@mui/icons-material": "^7.0.2", "@mui/material": "^7.0.2", "axios": "^1.8.4", "draft-js": "^0.11.7", "draftjs-to-html": "^0.9.1", "html-to-draftjs": "^1.5.0", "react": "^19.0.0", "react-dom": "^19.0.0", "react-draft-wysiwyg": "^1.15.0", "react-icons": "^5.5.0", "react-router-dom": "^7.5.0", "recharts": "^2.15.2"}, "devDependencies": {"@eslint/js": "^9.21.0", "@types/react": "^19.0.10", "@types/react-dom": "^19.0.4", "@vitejs/plugin-react": "^4.3.4", "eslint": "^9.21.0", "eslint-plugin-react-hooks": "^5.1.0", "eslint-plugin-react-refresh": "^0.4.19", "globals": "^15.15.0", "vite": "^6.2.0"}}