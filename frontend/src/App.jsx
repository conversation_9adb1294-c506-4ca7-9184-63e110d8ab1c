import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';
import { AuthProvider } from './context/AuthContext';
import { NoteProvider } from './context/NoteContext';
import Navbar from './components/Navbar';
import HomePage from './pages/HomePage';
import LoginPage from './pages/LoginPage';
import RegisterPage from './pages/RegisterPage';
import DashboardPage from './pages/DashboardPage';
import EnhancedDashboardPage from './pages/EnhancedDashboardPage';
import NewNotePage from './pages/NewNotePage';
import EditNotePage from './pages/EditNotePage';
import NoteDetailPage from './pages/NoteDetailPage';
import RoadmapsListPage from './pages/RoadmapsListPage';
import RoadmapPage from './pages/RoadmapPage';

// Protected route component
const ProtectedRoute = ({ children }) => {
  const isAuthenticated = localStorage.getItem('token');
  return isAuthenticated ? children : <Navigate to="/login" />;
};

// Auth redirect component - redirects to dashboard if logged in, otherwise shows homepage
const AuthRedirect = () => {
  const isAuthenticated = localStorage.getItem('token');
  return isAuthenticated ? <Navigate to="/dashboard" /> : <HomePage />;
};



function App() {
  return (
    <Router>
      <AuthProvider>
        <NoteProvider>
          <div className="min-h-screen bg-gray-50">
            <Navbar />
            <main>
              <Routes>
                <Route path="/" element={<AuthRedirect />} />
                <Route path="/login" element={<LoginPage />} />
                <Route path="/register" element={<RegisterPage />} />
                <Route
                  path="/dashboard"
                  element={
                    <ProtectedRoute>
                      <EnhancedDashboardPage />
                    </ProtectedRoute>
                  }
                />
                <Route
                  path="/notes"
                  element={
                    <ProtectedRoute>
                      <DashboardPage />
                    </ProtectedRoute>
                  }
                />
                <Route
                  path="/notes/new"
                  element={
                    <ProtectedRoute>
                      <NewNotePage />
                    </ProtectedRoute>
                  }
                />
                <Route
                  path="/notes/:id"
                  element={
                    <ProtectedRoute>
                      <NoteDetailPage />
                    </ProtectedRoute>
                  }
                />
                <Route
                  path="/notes/:id/edit"
                  element={
                    <ProtectedRoute>
                      <EditNotePage />
                    </ProtectedRoute>
                  }
                />
                <Route
                  path="/roadmaps"
                  element={
                    <ProtectedRoute>
                      <RoadmapsListPage />
                    </ProtectedRoute>
                  }
                />
                <Route
                  path="/roadmaps/:id"
                  element={
                    <ProtectedRoute>
                      <RoadmapPage />
                    </ProtectedRoute>
                  }
                />
              </Routes>
            </main>
          </div>
        </NoteProvider>
      </AuthProvider>
    </Router>
  );
}

export default App;
