body {
  margin: 0;
  padding: 0;
  font-family: '<PERSON><PERSON>', 'Helvetica', 'Arial', sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* TipTap Editor Styles */
.ProseMirror {
  outline: none;
  min-height: 200px;
}

.ProseMirror p {
  margin: 1em 0;
}

.ProseMirror h1 {
  font-size: 2em;
  margin: 0.67em 0;
}

.ProseMirror h2 {
  font-size: 1.5em;
  margin: 0.75em 0;
}

.ProseMirror h3 {
  font-size: 1.17em;
  margin: 0.83em 0;
}

.ProseMirror blockquote {
  border-left: 3px solid #ddd;
  margin-left: 0;
  padding-left: 1em;
  color: #666;
}

.ProseMirror ul,
.ProseMirror ol {
  padding-left: 1.5em;
}

.ProseMirror img {
  max-width: 100%;
  height: auto;
}

.ProseMirror a {
  color: #4f46e5;
  text-decoration: underline;
}

.ProseMirror p.is-editor-empty:first-child::before {
  color: #adb5bd;
  content: attr(data-placeholder);
  float: left;
  height: 0;
  pointer-events: none;
}