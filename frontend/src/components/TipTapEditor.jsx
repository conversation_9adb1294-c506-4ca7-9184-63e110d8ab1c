import { useE<PERSON><PERSON>, EditorContent } from '@tiptap/react';
import StarterKit from '@tiptap/starter-kit';
import Link from '@tiptap/extension-link';
import Image from '@tiptap/extension-image';
import Placeholder from '@tiptap/extension-placeholder';
import { useEffect } from 'react';
import {
  Box,
  Button,
  Divider,
  Paper,
  ToggleButton,
  ToggleButtonGroup,
  Tooltip,
} from '@mui/material';
import {
  FormatBold,
  FormatItalic,
  FormatListBulleted,
  FormatListNumbered,
  FormatQuote,
  Link as LinkIcon,
  Image as ImageIcon,
  Undo,
  Redo,
  Title,
  FormatClear,
} from '@mui/icons-material';

const MenuBar = ({ editor }) => {
  if (!editor) {
    return null;
  }

  return (
    <Box sx={{ p: 1, borderBottom: '1px solid #e0e0e0' }}>
      <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5, alignItems: 'center' }}>
        <ToggleButtonGroup size="small" aria-label="text formatting">
          <Tooltip title="Bold">
            <ToggleButton
              value="bold"
              selected={editor.isActive('bold')}
              onClick={() => editor.chain().focus().toggleBold().run()}
              aria-label="bold"
            >
              <FormatBold fontSize="small" />
            </ToggleButton>
          </Tooltip>
          <Tooltip title="Italic">
            <ToggleButton
              value="italic"
              selected={editor.isActive('italic')}
              onClick={() => editor.chain().focus().toggleItalic().run()}
              aria-label="italic"
            >
              <FormatItalic fontSize="small" />
            </ToggleButton>
          </Tooltip>
        </ToggleButtonGroup>

        <Divider orientation="vertical" flexItem sx={{ mx: 0.5 }} />

        <ToggleButtonGroup size="small" aria-label="heading formatting">
          <Tooltip title="Heading 1">
            <ToggleButton
              value="h1"
              selected={editor.isActive('heading', { level: 1 })}
              onClick={() => editor.chain().focus().toggleHeading({ level: 1 }).run()}
              aria-label="heading 1"
            >
              <Title fontSize="small" />1
            </ToggleButton>
          </Tooltip>
          <Tooltip title="Heading 2">
            <ToggleButton
              value="h2"
              selected={editor.isActive('heading', { level: 2 })}
              onClick={() => editor.chain().focus().toggleHeading({ level: 2 }).run()}
              aria-label="heading 2"
            >
              <Title fontSize="small" />2
            </ToggleButton>
          </Tooltip>
          <Tooltip title="Heading 3">
            <ToggleButton
              value="h3"
              selected={editor.isActive('heading', { level: 3 })}
              onClick={() => editor.chain().focus().toggleHeading({ level: 3 }).run()}
              aria-label="heading 3"
            >
              <Title fontSize="small" />3
            </ToggleButton>
          </Tooltip>
        </ToggleButtonGroup>

        <Divider orientation="vertical" flexItem sx={{ mx: 0.5 }} />

        <ToggleButtonGroup size="small" aria-label="list formatting">
          <Tooltip title="Bullet List">
            <ToggleButton
              value="bulletList"
              selected={editor.isActive('bulletList')}
              onClick={() => editor.chain().focus().toggleBulletList().run()}
              aria-label="bullet list"
            >
              <FormatListBulleted fontSize="small" />
            </ToggleButton>
          </Tooltip>
          <Tooltip title="Numbered List">
            <ToggleButton
              value="orderedList"
              selected={editor.isActive('orderedList')}
              onClick={() => editor.chain().focus().toggleOrderedList().run()}
              aria-label="ordered list"
            >
              <FormatListNumbered fontSize="small" />
            </ToggleButton>
          </Tooltip>
        </ToggleButtonGroup>

        <Divider orientation="vertical" flexItem sx={{ mx: 0.5 }} />

        <ToggleButtonGroup size="small" aria-label="block formatting">
          <Tooltip title="Blockquote">
            <ToggleButton
              value="blockquote"
              selected={editor.isActive('blockquote')}
              onClick={() => editor.chain().focus().toggleBlockquote().run()}
              aria-label="blockquote"
            >
              <FormatQuote fontSize="small" />
            </ToggleButton>
          </Tooltip>
          <Tooltip title="Add Link">
            <ToggleButton
              value="link"
              selected={editor.isActive('link')}
              onClick={() => {
                const url = window.prompt('URL');
                if (url) {
                  editor.chain().focus().setLink({ href: url }).run();
                } else if (editor.isActive('link')) {
                  editor.chain().focus().unsetLink().run();
                }
              }}
              aria-label="link"
            >
              <LinkIcon fontSize="small" />
            </ToggleButton>
          </Tooltip>
          <Tooltip title="Add Image">
            <ToggleButton
              value="image"
              onClick={() => {
                const url = window.prompt('Image URL');
                if (url) {
                  editor.chain().focus().setImage({ src: url }).run();
                }
              }}
              aria-label="image"
            >
              <ImageIcon fontSize="small" />
            </ToggleButton>
          </Tooltip>
        </ToggleButtonGroup>

        <Divider orientation="vertical" flexItem sx={{ mx: 0.5 }} />

        <ToggleButtonGroup size="small" aria-label="history">
          <Tooltip title="Undo">
            <ToggleButton
              value="undo"
              onClick={() => editor.chain().focus().undo().run()}
              disabled={!editor.can().undo()}
              aria-label="undo"
            >
              <Undo fontSize="small" />
            </ToggleButton>
          </Tooltip>
          <Tooltip title="Redo">
            <ToggleButton
              value="redo"
              onClick={() => editor.chain().focus().redo().run()}
              disabled={!editor.can().redo()}
              aria-label="redo"
            >
              <Redo fontSize="small" />
            </ToggleButton>
          </Tooltip>
        </ToggleButtonGroup>

        <Divider orientation="vertical" flexItem sx={{ mx: 0.5 }} />

        <Tooltip title="Clear Formatting">
          <Button
            size="small"
            variant="outlined"
            onClick={() => editor.chain().focus().clearNodes().unsetAllMarks().run()}
            startIcon={<FormatClear fontSize="small" />}
          >
            Clear
          </Button>
        </Tooltip>
      </Box>
    </Box>
  );
};

const TipTapEditor = ({ value, onChange, placeholder }) => {
  const editor = useEditor({
    extensions: [
      StarterKit,
      Link.configure({
        openOnClick: false,
      }),
      Image,
      Placeholder.configure({
        placeholder,
      }),
    ],
    content: value,
    onUpdate: ({ editor }) => {
      onChange(editor.getHTML());
    },
  });

  useEffect(() => {
    if (editor && value !== editor.getHTML()) {
      editor.commands.setContent(value);
    }
  }, [value, editor]);

  return (
    <Paper variant="outlined" sx={{ overflow: 'hidden' }}>
      <MenuBar editor={editor} />
      <Box sx={{ p: 2, minHeight: '200px' }}>
        <EditorContent editor={editor} />
      </Box>
    </Paper>
  );
};

export default TipTapEditor;
