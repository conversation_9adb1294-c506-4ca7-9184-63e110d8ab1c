import { useState, useContext } from 'react';
import { NoteContext } from '../context/NoteContext';
import { Link as RouterLink } from 'react-router-dom';
import ScoreIndicatorGroup from './ScoreIndicatorGroup';
import { calculateFeatureScores } from '../utils/scoreUtils';
import {
  Box,
  Card,
  CardContent,
  CardActionArea,
  Typography,
  IconButton,
  Chip,
  Stack,
  Dialog,
  DialogActions,
  DialogContent,
  DialogContentText,
  DialogTitle,
  Button,
  Divider,
  useTheme,
} from '@mui/material';
import { Delete as DeleteIcon, Edit as EditIcon } from '@mui/icons-material';

const NoteCard = ({ note, onDelete }) => {
  const { operationLoading } = useContext(NoteContext);
  const [openDialog, setOpenDialog] = useState(false);
  const theme = useTheme();

  // Calculate feature scores
  const featureScores = calculateFeatureScores(note);

  const handleDeleteClick = () => {
    setOpenDialog(true);
  };

  const handleCloseDialog = () => {
    setOpenDialog(false);
  };

  const handleConfirmDelete = async () => {
    try {
      await onDelete(note._id);
      setOpenDialog(false);
    } catch (error) {
      console.error('Error deleting note:', error);
    }
  };

  // Format date
  const formatDate = (dateString) => {
    const options = { year: 'numeric', month: 'short', day: 'numeric' };
    return new Date(dateString).toLocaleDateString(undefined, options);
  };

  // Strip HTML tags for preview
  const stripHtml = (html) => {
    const doc = new DOMParser().parseFromString(html, 'text/html');
    return doc.body.textContent || '';
  };

  const contentPreview = stripHtml(note.content);
  const truncatedContent = contentPreview.length > 150
    ? `${contentPreview.substring(0, 150)}...`
    : contentPreview;

  return (
    <>
      <Card sx={{ height: '100%', display: 'flex', flexDirection: 'column' }}>
        <CardContent sx={{ flex: '1 0 auto', pb: 1 }}>
          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start', mb: 1 }}>
            <Typography
              variant="h6"
              component={RouterLink}
              to={`/notes/${note._id}`}
              sx={{
                color: 'primary.main',
                textDecoration: 'none',
                '&:hover': { color: 'primary.dark' },
                fontWeight: 600,
              }}
            >
              {note.title}
            </Typography>
            <Box>
              <IconButton
                size="small"
                component={RouterLink}
                to={`/notes/${note._id}/edit`}
                aria-label="edit"
              >
                <EditIcon fontSize="small" />
              </IconButton>
              <IconButton
                size="small"
                onClick={handleDeleteClick}
                disabled={operationLoading.delete}
                aria-label="delete"
              >
                <DeleteIcon fontSize="small" />
              </IconButton>
            </Box>
          </Box>

          <Typography
            variant="body2"
            color="text.secondary"
            sx={{
              mb: 2,
              overflow: 'hidden',
              textOverflow: 'ellipsis',
              display: '-webkit-box',
              WebkitLineClamp: 2,
              WebkitBoxOrient: 'vertical',
              height: '2.5rem'
            }}
          >
            {truncatedContent}
          </Typography>

          <Stack direction="row" spacing={0.5} flexWrap="wrap" sx={{ mb: 1 }}>
            {note.tags.map((tag) => (
              <Chip
                key={tag}
                label={`#${tag}`}
                size="small"
                sx={{
                  bgcolor: 'primary.50',
                  color: 'primary.800',
                  fontSize: '0.7rem',
                  height: '1.5rem',
                  mb: 0.5
                }}
              />
            ))}
          </Stack>

          {/* Feature Score Indicators */}
          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 1 }}>
            <ScoreIndicatorGroup
              scores={featureScores}
              size="small"
              showLabels={false}
              spacing={1}
            />
            <Typography variant="caption" color="text.secondary">
              Updated: {formatDate(note.updatedAt)}
            </Typography>
          </Box>
        </CardContent>
        <CardActionArea
          component={RouterLink}
          to={`/notes/${note._id}`}
          sx={{ mt: 'auto', p: 1 }}
        >
          <Typography variant="button" color="primary" sx={{ fontSize: '0.75rem' }}>
            View Details
          </Typography>
        </CardActionArea>
      </Card>

      {/* Confirmation Dialog */}
      <Dialog
        open={openDialog}
        onClose={handleCloseDialog}
      >
        <DialogTitle>Delete Note</DialogTitle>
        <DialogContent>
          <DialogContentText>
            Are you sure you want to delete "{note.title}"? This action cannot be undone.
          </DialogContentText>
        </DialogContent>
        <DialogActions>
          <Button onClick={handleCloseDialog}>Cancel</Button>
          <Button onClick={handleConfirmDelete} color="error" disabled={operationLoading.delete}>
            {operationLoading.delete ? 'Deleting...' : 'Delete'}
          </Button>
        </DialogActions>
      </Dialog>
    </>
  );
};

export default NoteCard;
