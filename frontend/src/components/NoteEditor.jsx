import { useState, useEffect, useContext } from 'react';
import { NoteContext } from '../context/NoteContext';
import { extractTags } from '../api/aiApi';
import TipTapEditor from './TipTapEditor';
import {
  Box,
  Button,
  Chip,
  Paper,
  Stack,
  TextField,
  Typography,
  InputBase,
  IconButton,
  Divider,
  Alert,
  Snackbar,
} from '@mui/material';
import { Add as AddIcon, AutoAwesome as AutoAwesomeIcon } from '@mui/icons-material';

const NoteEditor = ({ note, onSave, isNew = false }) => {
  const [title, setTitle] = useState('');
  const [content, setContent] = useState('');
  const [tags, setTags] = useState([]);
  const [newTag, setNewTag] = useState('');
  const [loading, setLoading] = useState(false);
  const [alertOpen, setAlertOpen] = useState(false);
  const [alertMessage, setAlertMessage] = useState('');
  const { loading: noteLoading } = useContext(NoteContext);

  useEffect(() => {
    if (note) {
      setTitle(note.title || '');
      setContent(note.content || '');
      setTags(note.tags || []);
    } else {
      setTitle('');
      setContent('');
      setTags([]);
    }
  }, [note]);

  const handleSave = async () => {
    if (!title.trim() || !content.trim()) {
      setAlertMessage('Please provide both title and content');
      setAlertOpen(true);
      return;
    }

    const noteData = {
      title,
      content,
      tags,
    };

    try {
      await onSave(noteData);
      if (isNew) {
        setTitle('');
        setContent('');
        setTags([]);
      }
    } catch (error) {
      console.error('Error saving note:', error);
      setAlertMessage('Error saving note: ' + error.message);
      setAlertOpen(true);
    }
  };

  const handleAddTag = () => {
    if (newTag.trim() && !tags.includes(newTag.trim())) {
      setTags([...tags, newTag.trim()]);
      setNewTag('');
    }
  };

  const handleRemoveTag = (tagToRemove) => {
    setTags(tags.filter((tag) => tag !== tagToRemove));
  };

  const handleExtractTags = async () => {
    if (!content.trim()) {
      setAlertMessage('Please add some content to extract tags');
      setAlertOpen(true);
      return;
    }

    setLoading(true);
    try {
      const { tags: extractedTags } = await extractTags(content);
      // Add only new tags
      const newTags = extractedTags.filter((tag) => !tags.includes(tag));
      setTags([...tags, ...newTags]);
    } catch (error) {
      console.error('Error extracting tags:', error);
      setAlertMessage('Error extracting tags: ' + error.message);
      setAlertOpen(true);
    } finally {
      setLoading(false);
    }
  };

  const handleCloseAlert = () => {
    setAlertOpen(false);
  };

  const handleContentChange = (htmlContent) => {
    setContent(htmlContent);
  };

  return (
    <Paper elevation={2} sx={{ p: 3 }}>
      <Box sx={{ mb: 3 }}>
        <Typography variant="subtitle1" fontWeight="medium" gutterBottom>
          Title
        </Typography>
        <TextField
          fullWidth
          id="title"
          value={title}
          onChange={(e) => setTitle(e.target.value)}
          placeholder="Enter note title"
          variant="outlined"
          size="small"
        />
      </Box>

      <Box sx={{ mb: 4 }}>
        <Typography variant="subtitle1" fontWeight="medium" gutterBottom>
          Content
        </Typography>
        <TipTapEditor
          value={content}
          onChange={handleContentChange}
          placeholder="Write your note here..."
        />
      </Box>

      <Box sx={{ mb: 4 }}>
        <Typography variant="subtitle1" fontWeight="medium" gutterBottom>
          Tags
        </Typography>

        <Stack direction="row" spacing={1} flexWrap="wrap" sx={{ mb: 2 }}>
          {tags.map((tag) => (
            <Chip
              key={tag}
              label={`#${tag}`}
              onDelete={() => handleRemoveTag(tag)}
              size="small"
              sx={{ mb: 1 }}
            />
          ))}
        </Stack>

        <Paper
          variant="outlined"
          sx={{ p: 0.5, display: 'flex', alignItems: 'center', mb: 2 }}
        >
          <InputBase
            sx={{ ml: 1, flex: 1 }}
            placeholder="Add a tag"
            value={newTag}
            onChange={(e) => setNewTag(e.target.value)}
            onKeyPress={(e) => e.key === 'Enter' && handleAddTag()}
          />
          <Divider sx={{ height: 28, m: 0.5 }} orientation="vertical" />
          <IconButton color="primary" onClick={handleAddTag} edge="end">
            <AddIcon />
          </IconButton>
        </Paper>

        <Button
          variant="outlined"
          startIcon={<AutoAwesomeIcon />}
          onClick={handleExtractTags}
          disabled={loading || !content.trim()}
          size="small"
        >
          {loading ? 'Extracting...' : 'Extract Tags with AI'}
        </Button>
      </Box>

      <Box sx={{ display: 'flex', justifyContent: 'flex-end' }}>
        <Button
          variant="contained"
          onClick={handleSave}
          disabled={noteLoading || !title.trim() || !content.trim()}
        >
          {noteLoading ? 'Saving...' : 'Save Note'}
        </Button>
      </Box>

      <Snackbar open={alertOpen} autoHideDuration={6000} onClose={handleCloseAlert}>
        <Alert onClose={handleCloseAlert} severity="error" sx={{ width: '100%' }}>
          {alertMessage}
        </Alert>
      </Snackbar>
    </Paper>
  );
};

export default NoteEditor;
