import React from 'react';
import { Box, Tooltip, Typography, useTheme } from '@mui/material';
import ScoreIndicator from './ScoreIndicator';

// Component to display a group of score indicators
const ScoreIndicatorGroup = ({ 
  scores, 
  size = 'medium', 
  showLabels = false,
  spacing = 1,
  direction = 'row'
}) => {
  const theme = useTheme();
  
  // Calculate average score if there are any scores
  const validScores = Object.values(scores).filter(score => 
    score !== undefined && score !== null && !isNaN(score)
  );
  
  const averageScore = validScores.length > 0 
    ? Math.round(validScores.reduce((sum, score) => sum + score, 0) / validScores.length) 
    : null;

  // Get color based on average score
  const getAverageColor = (score) => {
    if (score === null) return theme.palette.grey[400];
    if (score >= 80) return theme.palette.success.main;
    if (score >= 50) return theme.palette.warning.main;
    return theme.palette.error.main;
  };

  const averageColor = getAverageColor(averageScore);

  return (
    <Box sx={{ display: 'flex', flexDirection: 'column', alignItems: 'flex-start' }}>
      <Box 
        sx={{ 
          display: 'flex', 
          flexDirection: direction, 
          gap: spacing, 
          alignItems: 'center',
          flexWrap: 'wrap'
        }}
      >
        <ScoreIndicator 
          type="reflective" 
          score={scores.reflective} 
          size={size} 
          showLabel={showLabels} 
        />
        <ScoreIndicator 
          type="concept" 
          score={scores.concept} 
          size={size} 
          showLabel={showLabels} 
        />
        <ScoreIndicator 
          type="socratic" 
          score={scores.socratic} 
          size={size} 
          showLabel={showLabels} 
        />
        <ScoreIndicator 
          type="teach" 
          score={scores.teach} 
          size={size} 
          showLabel={showLabels} 
        />
        
        {averageScore !== null && (
          <Tooltip title={`Average score: ${averageScore}%`}>
            <Box sx={{ 
              display: 'flex', 
              alignItems: 'center',
              ml: direction === 'row' ? 1 : 0,
              mt: direction === 'column' ? 1 : 0
            }}>
              <Typography 
                variant={size === 'small' ? 'caption' : 'body2'} 
                sx={{ 
                  fontWeight: 'bold', 
                  color: averageColor,
                  display: 'flex',
                  alignItems: 'center'
                }}
              >
                Avg: {averageScore}%
              </Typography>
            </Box>
          </Tooltip>
        )}
      </Box>
    </Box>
  );
};

export default ScoreIndicatorGroup;
