import {
  Box,
  Card,
  CardContent,
  Skeleton,
  Stack,
} from '@mui/material';

const NoteCardSkeleton = () => {
  return (
    <Card sx={{ height: '100%', display: 'flex', flexDirection: 'column' }}>
      <CardContent sx={{ flex: '1 0 auto', pb: 1 }}>
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start', mb: 1 }}>
          <Skeleton variant="text" width="70%" height={32} />
          <Box>
            <Skeleton variant="circular" width={24} height={24} sx={{ display: 'inline-block', mr: 1 }} />
            <Skeleton variant="circular" width={24} height={24} sx={{ display: 'inline-block' }} />
          </Box>
        </Box>

        <Skeleton variant="text" sx={{ mb: 1 }} />
        <Skeleton variant="text" sx={{ mb: 2 }} />

        <Stack direction="row" spacing={0.5} flexWrap="wrap" sx={{ mb: 1 }}>
          <Skeleton variant="rounded" width={60} height={20} sx={{ mr: 0.5, mb: 0.5 }} />
          <Skeleton variant="rounded" width={40} height={20} sx={{ mr: 0.5, mb: 0.5 }} />
          <Skeleton variant="rounded" width={50} height={20} sx={{ mb: 0.5 }} />
        </Stack>

        <Skeleton variant="text" width="40%" />
      </CardContent>
      <Box sx={{ mt: 'auto', p: 1 }}>
        <Skeleton variant="text" width={80} height={24} />
      </Box>
    </Card>
  );
};

export default NoteCardSkeleton;
