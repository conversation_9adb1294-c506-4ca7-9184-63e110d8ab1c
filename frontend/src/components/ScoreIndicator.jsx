import React from 'react';
import { Box, Tooltip, Typography, useTheme, alpha } from '@mui/material';
import {
  Psychology as PsychologyIcon,
  Lightbulb as LightbulbIcon,
  QuestionAnswer as QuestionAnswerIcon,
  School as SchoolIcon,
} from '@mui/icons-material';

// Component to display a score indicator with an icon
const ScoreIndicator = ({ type, score, size = 'medium', showLabel = false }) => {
  const theme = useTheme();
  
  // Define icon, color, and label based on type
  const getIconProps = (type) => {
    switch (type) {
      case 'reflective':
        return {
          icon: <PsychologyIcon fontSize={size === 'small' ? 'small' : 'medium'} />,
          color: theme.palette.primary.main,
          label: 'Reflective Prompts',
          tooltip: 'Score based on reflective prompt responses'
        };
      case 'concept':
        return {
          icon: <LightbulbIcon fontSize={size === 'small' ? 'small' : 'medium'} />,
          color: theme.palette.info.main,
          label: 'Concept Rebuilder',
          tooltip: 'Score based on concept rebuilding exercise'
        };
      case 'socratic':
        return {
          icon: <QuestionAnswerIcon fontSize={size === 'small' ? 'small' : 'medium'} />,
          color: theme.palette.warning.main,
          label: 'Socratic Questions',
          tooltip: 'Score based on Socratic question responses'
        };
      case 'teach':
        return {
          icon: <SchoolIcon fontSize={size === 'small' ? 'small' : 'medium'} />,
          color: theme.palette.success.main,
          label: 'Teaching AI',
          tooltip: 'Score based on teaching the AI exercise'
        };
      default:
        return {
          icon: <SchoolIcon fontSize={size === 'small' ? 'small' : 'medium'} />,
          color: theme.palette.grey[500],
          label: 'Unknown',
          tooltip: 'Score'
        };
    }
  };

  // Get color based on score
  const getScoreColor = (score) => {
    if (score === undefined || score === null) return theme.palette.grey[400];
    if (score >= 80) return theme.palette.success.main;
    if (score >= 50) return theme.palette.warning.main;
    return theme.palette.error.main;
  };

  const { icon, color, label, tooltip } = getIconProps(type);
  const scoreColor = getScoreColor(score);
  const hasScore = score !== undefined && score !== null;
  
  // Size adjustments
  const dimensions = size === 'small' ? 28 : size === 'large' ? 48 : 36;
  const fontSize = size === 'small' ? '0.7rem' : size === 'large' ? '1rem' : '0.875rem';
  const iconSize = size === 'small' ? 14 : size === 'large' ? 24 : 18;

  return (
    <Tooltip title={`${tooltip}: ${hasScore ? score + '%' : 'Not assessed'}`}>
      <Box sx={{ display: 'flex', flexDirection: 'column', alignItems: 'center' }}>
        <Box
          sx={{
            width: dimensions,
            height: dimensions,
            borderRadius: '50%',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            bgcolor: alpha(color, 0.1),
            color: hasScore ? scoreColor : color,
            position: 'relative',
            border: hasScore ? `2px solid ${scoreColor}` : `1px solid ${alpha(color, 0.5)}`,
          }}
        >
          {icon}
          {hasScore && (
            <Box
              sx={{
                position: 'absolute',
                bottom: -2,
                right: -2,
                bgcolor: scoreColor,
                color: 'white',
                borderRadius: '50%',
                width: iconSize,
                height: iconSize,
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                fontSize: fontSize,
                fontWeight: 'bold',
              }}
            >
              {Math.round(score / 10)}
            </Box>
          )}
        </Box>
        {showLabel && (
          <Typography 
            variant="caption" 
            sx={{ 
              mt: 0.5, 
              fontSize: size === 'small' ? '0.6rem' : '0.75rem',
              color: 'text.secondary',
              textAlign: 'center',
              maxWidth: dimensions * 2,
              overflow: 'hidden',
              textOverflow: 'ellipsis',
              whiteSpace: 'nowrap'
            }}
          >
            {label}
          </Typography>
        )}
      </Box>
    </Tooltip>
  );
};

export default ScoreIndicator;
