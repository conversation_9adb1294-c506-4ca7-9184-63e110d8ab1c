import { useContext } from 'react';
import { Link as RouterLink } from 'react-router-dom';
import { AuthContext } from '../context/AuthContext';
import {
  AppBar,
  Toolbar,
  Typography,
  Button,
  Container,
  Box,
  IconButton,
  Tooltip,
  Divider
} from '@mui/material';
import {
  School as SchoolIcon,
  Dashboard as DashboardIcon,
  Note as NoteIcon
} from '@mui/icons-material';

const Navbar = () => {
  const { isAuthenticated, logout } = useContext(AuthContext);

  return (
    <AppBar position="static">
      <Container maxWidth="lg">
        <Toolbar disableGutters>
          <Typography
            variant="h6"
            component={RouterLink}
            to="/"
            sx={{
              flexGrow: 1,
              textDecoration: 'none',
              color: 'white',
              fontWeight: 'bold'
            }}
          >
            AI Notes
          </Typography>
          <Box>
            {isAuthenticated ? (
              <>
                <Tooltip title="Dashboard">
                  <IconButton
                    color="inherit"
                    component={RouterLink}
                    to="/dashboard"
                    sx={{ mr: 1 }}
                  >
                    <DashboardIcon />
                  </IconButton>
                </Tooltip>
                <Tooltip title="Notes">
                  <IconButton
                    color="inherit"
                    component={RouterLink}
                    to="/notes"
                    sx={{ mr: 1 }}
                  >
                    <NoteIcon />
                  </IconButton>
                </Tooltip>
                <Tooltip title="Learning Roadmaps">
                  <IconButton
                    color="inherit"
                    component={RouterLink}
                    to="/roadmaps"
                    sx={{ mr: 1 }}
                  >
                    <SchoolIcon />
                  </IconButton>
                </Tooltip>
                <Button
                  color="inherit"
                  onClick={logout}
                >
                  Logout
                </Button>
              </>
            ) : (
              <>
                <Button
                  color="inherit"
                  component={RouterLink}
                  to="/login"
                  sx={{ mr: 2 }}
                >
                  Login
                </Button>
                <Button
                  color="inherit"
                  component={RouterLink}
                  to="/register"
                >
                  Register
                </Button>
              </>
            )}
          </Box>
        </Toolbar>
      </Container>
    </AppBar>
  );
};

export default Navbar;
