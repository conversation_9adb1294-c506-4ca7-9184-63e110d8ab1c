import { useState, useContext, useEffect, useRef } from 'react';
import { NoteContext } from '../context/NoteContext';
import { getNoteById } from '../api/noteApi';
import ScoreIndicatorGroup from './ScoreIndicatorGroup';
import { calculateFeatureScores } from '../utils/scoreUtils';
import {
  generateReflectivePrompts,
  generateConceptRebuild,
  generateSocraticQuestions,
  startTeachSession,
  submitExplanation,
  submitQuestionResponse,
  generateFeedback,
  generateRoadmap,
} from '../api/aiApi';
import { createRoadmap } from '../api/roadmapApi';
import { useNavigate } from 'react-router-dom';
import {
  Box,
  Button,
  Paper,
  Typography,
  Tabs,
  Tab,
  TextField,
  Alert,
  Card,
  CardContent,
  CircularProgress,

  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  LinearProgress,
} from '@mui/material';
import {
  Psychology as PsychologyIcon,
  Lightbulb as LightbulbIcon,
  QuestionAnswer as QuestionAnswerIcon,
  School as SchoolIcon,
  CheckCircleOutline,
  InfoOutlined,
} from '@mui/icons-material';

const AiFeatures = ({ note: initialNote, refreshNote }) => {
  const { updateNoteSuggestions } = useContext(NoteContext);
  const navigate = useNavigate();
  const [note, setNote] = useState(initialNote);
  const [activeTab, setActiveTab] = useState('reflective');
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [success, setSuccess] = useState(null);
  const [localResponses, setLocalResponses] = useState({});
  const [featureScores, setFeatureScores] = useState({
    reflective: null,
    concept: null,
    socratic: null,
    teach: null
  });

  // Teach AI state
  const [teachStep, setTeachStep] = useState('initial'); // initial, explanation, questions, feedback
  const [teachSession, setTeachSession] = useState({
    userExplanation: '',
    followUpQuestions: [],
    questionHistory: [],
    currentQuestionIndex: 0,
    feedback: {
      strengths: [],
      improvements: [],
      suggestedRevision: '',
      confidenceScore: 0,
    },
    summary: '',
    completed: false,
  });

  // Debounce timers
  const saveTimerRef = useRef(null);

  // Update local note state when the prop changes
  useEffect(() => {
    if (!initialNote) return;

    // Make sure aiSuggestions is initialized
    if (!initialNote.aiSuggestions) {
      initialNote.aiSuggestions = {
        reflectivePrompts: [],
        conceptRebuild: {},
        socraticQuestions: [],
        teachSession: {}
      };
    }

    setNote(initialNote);

    // Initialize local responses from the note
    const initialResponses = {};
    if (initialNote.aiSuggestions.reflectivePrompts) {
      initialNote.aiSuggestions.reflectivePrompts.forEach((item, index) => {
        initialResponses[`reflective_${index}`] = item.response || '';
      });
    }
    if (initialNote.aiSuggestions.conceptRebuild) {
      initialResponses['concept'] = initialNote.aiSuggestions.conceptRebuild.userResponse || '';
    }
    if (initialNote.aiSuggestions.socraticQuestions) {
      initialNote.aiSuggestions.socraticQuestions.forEach((item, index) => {
        initialResponses[`socratic_${index}`] = item.response || '';
      });
    }

    // Initialize teach AI session if it exists
    if (initialNote.aiSuggestions.teachSession) {
      setTeachSession(initialNote.aiSuggestions.teachSession);
      if (initialNote.aiSuggestions.teachSession.feedback?.strengths?.length > 0) {
        setTeachStep('feedback');
      } else if (initialNote.aiSuggestions.teachSession.followUpQuestions?.length > 0) {
        setTeachStep('questions');
        // Initialize follow-up question responses
        initialNote.aiSuggestions.teachSession.followUpQuestions.forEach((item, index) => {
          initialResponses[`teach_question_${index}`] = item.response || '';
        });
      } else if (initialNote.aiSuggestions.teachSession.userExplanation) {
        setTeachStep('explanation');
      }
    }

    setLocalResponses(initialResponses);

    // Calculate and update feature scores
    const scores = calculateFeatureScores(initialNote);
    setFeatureScores(scores);

    // Don't call refreshNote here to avoid infinite loops
    // The parent component will update when needed
  }, [initialNote, refreshNote]);

  // Cleanup timers on unmount
  useEffect(() => {
    return () => {
      if (saveTimerRef.current) {
        clearTimeout(saveTimerRef.current);
      }
    };
  }, []);

  const handleTabChange = (_, newValue) => {
    setActiveTab(newValue);
  };

  // Generate reflective prompts
  const handleGenerateReflectivePrompts = async () => {
    setLoading(true);
    setError(null);
    try {
      const { reflectivePrompts } = await generateReflectivePrompts(note.content);
      await updateNoteSuggestions(note._id, { reflectivePrompts });
      // Refresh the note data from the server to ensure we have the latest version
      if (refreshNote) {
        await refreshNote();
      } else {
        const refreshedNote = await getNoteById(note._id);
        setNote(refreshedNote);
      }
    } catch (error) {
      setError('Failed to generate reflective prompts');
      console.error(error);
    } finally {
      setLoading(false);
    }
  };

  // Generate concept rebuild
  const handleGenerateConceptRebuild = async () => {
    setLoading(true);
    setError(null);
    try {
      const { conceptRebuild } = await generateConceptRebuild(note.content);
      await updateNoteSuggestions(note._id, { conceptRebuild });
      // Refresh the note data from the server to ensure we have the latest version
      if (refreshNote) {
        await refreshNote();
      } else {
        const refreshedNote = await getNoteById(note._id);
        setNote(refreshedNote);
      }
    } catch (error) {
      setError('Failed to generate simplified explanation');
      console.error(error);
    } finally {
      setLoading(false);
    }
  };

  // Generate Socratic questions
  const handleGenerateSocraticQuestions = async () => {
    setLoading(true);
    setError(null);
    try {
      const { socraticQuestions } = await generateSocraticQuestions(note.content);
      await updateNoteSuggestions(note._id, { socraticQuestions });
      // Refresh the note data from the server to ensure we have the latest version
      if (refreshNote) {
        await refreshNote();
      } else {
        const refreshedNote = await getNoteById(note._id);
        setNote(refreshedNote);
      }
    } catch (error) {
      setError('Failed to generate Socratic questions');
      console.error(error);
    } finally {
      setLoading(false);
    }
  };

  // Handle input change for reflective prompt
  const handleReflectiveInputChange = (index, value) => {
    // Update local state immediately
    setLocalResponses(prev => ({
      ...prev,
      [`reflective_${index}`]: value
    }));

    // Debounce the save operation
    if (saveTimerRef.current) {
      clearTimeout(saveTimerRef.current);
    }

    saveTimerRef.current = setTimeout(() => {
      saveReflectiveResponse(index, value);
    }, 1000); // Save after 1 second of inactivity
  };

  // Save response to reflective prompt
  const saveReflectiveResponse = async (index, response) => {
    const updatedPrompts = [...note.aiSuggestions.reflectivePrompts];
    updatedPrompts[index] = { ...updatedPrompts[index], response };

    // Update local state immediately for better UX
    const updatedNote = {
      ...note,
      aiSuggestions: {
        ...note.aiSuggestions,
        reflectivePrompts: updatedPrompts
      }
    };
    setNote(updatedNote);

    // Update feature scores
    const updatedScores = calculateFeatureScores(updatedNote);
    setFeatureScores(updatedScores);

    try {
      await updateNoteSuggestions(note._id, { reflectivePrompts: updatedPrompts });
      // Show success message
      setSuccess('Response saved successfully!');
      setTimeout(() => setSuccess(null), 3000); // Clear after 3 seconds

      // Don't refresh here to avoid potential infinite loops
      // Local state is already updated
    } catch (error) {
      console.error('Error saving response:', error);
      setError('Failed to save response. Please try again.');
      setTimeout(() => setError(null), 3000); // Clear after 3 seconds
    }
  };

  // Handle input change for concept rebuild
  const handleConceptInputChange = (value) => {
    // Update local state immediately
    setLocalResponses(prev => ({
      ...prev,
      concept: value
    }));

    // Debounce the save operation
    if (saveTimerRef.current) {
      clearTimeout(saveTimerRef.current);
    }

    saveTimerRef.current = setTimeout(() => {
      saveConceptResponse(value);
    }, 1000); // Save after 1 second of inactivity
  };

  // Save response to concept rebuild
  const saveConceptResponse = async (response) => {
    const updatedConcept = {
      ...note.aiSuggestions.conceptRebuild,
      userResponse: response,
    };

    // Update local state immediately for better UX
    const updatedNote = {
      ...note,
      aiSuggestions: {
        ...note.aiSuggestions,
        conceptRebuild: updatedConcept
      }
    };
    setNote(updatedNote);

    // Update feature scores
    const updatedScores = calculateFeatureScores(updatedNote);
    setFeatureScores(updatedScores);

    try {
      await updateNoteSuggestions(note._id, { conceptRebuild: updatedConcept });
      // Show success message
      setSuccess('Response saved successfully!');
      setTimeout(() => setSuccess(null), 3000); // Clear after 3 seconds

      // Don't refresh here to avoid potential infinite loops
      // Local state is already updated
    } catch (error) {
      console.error('Error saving response:', error);
      setError('Failed to save response. Please try again.');
      setTimeout(() => setError(null), 3000); // Clear after 3 seconds
    }
  };

  // Handle input change for Socratic question
  const handleSocraticInputChange = (index, value) => {
    // Update local state immediately
    setLocalResponses(prev => ({
      ...prev,
      [`socratic_${index}`]: value
    }));

    // Debounce the save operation
    if (saveTimerRef.current) {
      clearTimeout(saveTimerRef.current);
    }

    saveTimerRef.current = setTimeout(() => {
      saveSocraticResponse(index, value);
    }, 1000); // Save after 1 second of inactivity
  };

  // Save response to Socratic question
  const saveSocraticResponse = async (index, response) => {
    const updatedQuestions = [...note.aiSuggestions.socraticQuestions];
    updatedQuestions[index] = { ...updatedQuestions[index], response };

    // Update local state immediately for better UX
    const updatedNote = {
      ...note,
      aiSuggestions: {
        ...note.aiSuggestions,
        socraticQuestions: updatedQuestions
      }
    };
    setNote(updatedNote);

    // Update feature scores
    const updatedScores = calculateFeatureScores(updatedNote);
    setFeatureScores(updatedScores);

    try {
      await updateNoteSuggestions(note._id, { socraticQuestions: updatedQuestions });
      // Show success message
      setSuccess('Response saved successfully!');
      setTimeout(() => setSuccess(null), 3000); // Clear after 3 seconds

      // Don't refresh here to avoid potential infinite loops
      // Local state is already updated
    } catch (error) {
      console.error('Error saving response:', error);
      setError('Failed to save response. Please try again.');
      setTimeout(() => setError(null), 3000); // Clear after 3 seconds
    }
  };

  // Start a new teach AI session
  const handleStartTeachSession = async () => {
    setLoading(true);
    setError(null);
    try {
      const { teachSession: newTeachSession } = await startTeachSession(note.content);

      // Update the teach session state
      setTeachSession(newTeachSession);
      setTeachStep('explanation');

      // Update the note with the new teach session
      await updateNoteSuggestions(note._id, { teachSession: newTeachSession });

      // if (refreshNote) {
      //   await refreshNote();
      // } else {
      //   const refreshedNote = await getNoteById(note._id);
      //   setNote(refreshedNote);
      // }
    } catch (error) {
      setError('Failed to start teach session');
      console.error(error);
    } finally {
      setLoading(false);
    }
  };

  // Submit user explanation and get follow-up questions
  const handleSubmitExplanation = async (explanation) => {
    setLoading(true);
    setError(null);
    try {
      const { teachSession: updatedTeachSession } = await submitExplanation(note.content, explanation);

      // Update the teach session state
      setTeachSession(updatedTeachSession);
      setTeachStep('questions');

      // Update the note with the updated teach session
      await updateNoteSuggestions(note._id, { teachSession: updatedTeachSession });

      // Update local note state
      const updatedNote = {
        ...note,
        aiSuggestions: {
          ...note.aiSuggestions,
          teachSession: updatedTeachSession
        }
      };
      setNote(updatedNote);

      // Update feature scores
      const updatedScores = calculateFeatureScores(updatedNote);
      setFeatureScores(updatedScores);

      // Don't refresh here to avoid potential infinite loops
      // Local state is already updated
    } catch (error) {
      setError('Failed to submit explanation');
      console.error(error);
    } finally {
      setLoading(false);
    }
  };

  // Handle input change for teach AI follow-up question
  const handleTeachQuestionInputChange = (index, value) => {
    // Update local state immediately
    setLocalResponses(prev => ({
      ...prev,
      [`teach_question_${index}`]: value
    }));

    // Update the follow-up questions in the teach session
    const updatedQuestions = [...teachSession.followUpQuestions];
    updatedQuestions[index] = { ...updatedQuestions[index], response: value };

    setTeachSession(prev => ({
      ...prev,
      followUpQuestions: updatedQuestions
    }));
  };

  // Submit response to current question and get next question
  const handleSubmitQuestionResponse = async () => {
    setLoading(true);
    setError(null);
    try {
      // Get the current question and response
      const currentQuestion = teachSession.followUpQuestions[0];
      const questionResponse = currentQuestion.response;

      if (!questionResponse || questionResponse.trim() === '') {
        setError('Please answer the question before proceeding');
        setLoading(false);
        return;
      }

      // Submit the response and get the next question
      const { teachSession: updatedTeachSession } = await submitQuestionResponse(
        note.content,
        teachSession.userExplanation,
        currentQuestion,
        questionResponse,
        teachSession.questionHistory
      );

      // Update the teach session state
      setTeachSession(updatedTeachSession);

      // Update the note with the updated teach session
      await updateNoteSuggestions(note._id, { teachSession: updatedTeachSession });

      // Clear the current question response in the local state
      setLocalResponses(prev => ({
        ...prev,
        'teach_question_0': ''
      }));
    } catch (error) {
      setError('Failed to submit response');
      console.error(error);
    } finally {
      setLoading(false);
    }
  };

  // End the teaching session and get feedback
  const handleEndTeachSession = async () => {
    setLoading(true);
    setError(null);
    try {
      // Get the current question and response
      const currentQuestion = teachSession.followUpQuestions[0];
      const questionResponse = currentQuestion.response;

      if (!questionResponse || questionResponse.trim() === '') {
        setError('Please answer the current question before ending the session');
        setLoading(false);
        return;
      }

      // Add the final question and response to the history
      const updatedHistory = [...teachSession.questionHistory, {
        question: currentQuestion.question,
        response: questionResponse
      }];

      // Generate feedback based on all questions and responses
      const { teachSession: updatedTeachSession } = await generateFeedback(
        note.content,
        teachSession.userExplanation,
        updatedHistory
      );

      // Update the teach session state
      setTeachSession(updatedTeachSession);
      setTeachStep('feedback');

      // Update the note with the updated teach session
      await updateNoteSuggestions(note._id, { teachSession: updatedTeachSession });

      // Update local note state
      const updatedNote = {
        ...note,
        aiSuggestions: {
          ...note.aiSuggestions,
          teachSession: updatedTeachSession
        }
      };
      setNote(updatedNote);

      // Update feature scores
      const updatedScores = calculateFeatureScores(updatedNote);
      setFeatureScores(updatedScores);

      // Don't refresh here to avoid potential infinite loops
      // Local state is already updated
    } catch (error) {
      setError('Failed to generate feedback');
      console.error(error);
    } finally {
      setLoading(false);
    }
  };

  // Reset the teach AI session
  const handleResetTeachSession = () => {
    setTeachStep('initial');
    setTeachSession({
      userExplanation: '',
      followUpQuestions: [],
      questionHistory: [],
      currentQuestionIndex: 0,
      feedback: {
        strengths: [],
        improvements: [],
        suggestedRevision: '',
        confidenceScore: 0,
      },
      summary: '',
      completed: false,
    });
    setLocalResponses(prev => {
      const newResponses = { ...prev };
      // Clear all teach question responses
      Object.keys(newResponses).forEach(key => {
        if (key.startsWith('teach_question_')) {
          delete newResponses[key];
        }
      });
      return newResponses;
    });
  };

  // Create a learning roadmap
  const handleCreateRoadmap = async () => {
    setLoading(true);
    setError(null);
    try {
      // Generate the roadmap
      const { roadmap } = await generateRoadmap(
        note.content,
        teachSession.userExplanation,
        teachSession.questionHistory,
        teachSession.feedback
      );

      // Save the roadmap to the database
      const savedRoadmap = await createRoadmap(note._id, roadmap);

      // Navigate to the roadmap page
      navigate(`/roadmaps/${savedRoadmap._id}`);
    } catch (error) {
      setError('Failed to create roadmap');
      console.error(error);
    } finally {
      setLoading(false);
    }
  };

  return (
    <Paper elevation={2} sx={{ p: 3 }}>
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
        <Typography variant="h5" fontWeight="bold">
          AI Learning Features
        </Typography>
        <ScoreIndicatorGroup
          scores={featureScores}
          size="small"
          showLabels={true}
          spacing={1.5}
        />
      </Box>

      {/* Tabs */}
      <Box sx={{ borderBottom: 1, borderColor: 'divider', mb: 3 }}>
        <Tabs
          value={activeTab}
          onChange={handleTabChange}
          aria-label="AI features tabs"
        >
          <Tab
            icon={<PsychologyIcon />}
            iconPosition="start"
            label="Reflective Prompts"
            value="reflective"
          />
          <Tab
            icon={<LightbulbIcon />}
            iconPosition="start"
            label="Concept Rebuilder"
            value="concept"
          />
          <Tab
            icon={<QuestionAnswerIcon />}
            iconPosition="start"
            label="Socratic Questions"
            value="socratic"
          />
          <Tab
            icon={<SchoolIcon />}
            iconPosition="start"
            label="Teach the AI"
            value="teach"
          />
        </Tabs>
      </Box>

      {/* Error message */}
      {error && (
        <Alert severity="error" sx={{ mb: 3 }}>
          {error}
        </Alert>
      )}

      {/* Success message */}
      {success && (
        <Alert severity="success" sx={{ mb: 3 }}>
          {success}
        </Alert>
      )}

      {/* Loading indicator */}
      {loading && (
        <Box sx={{ display: 'flex', justifyContent: 'center', my: 4 }}>
          <CircularProgress />
        </Box>
      )}

      {/* Tab content */}
      <Box sx={{ mb: 3, minHeight: '200px' }}>
        {/* Reflective Prompts */}
        {activeTab === 'reflective' && !loading && (
          <Box>
            <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
              <Typography variant="h6">
                Reflective Prompts
              </Typography>
              <Button
                variant="contained"
                onClick={handleGenerateReflectivePrompts}
                disabled={loading}
              >
                Generate Prompts
              </Button>
            </Box>

            {note.aiSuggestions?.reflectivePrompts?.length > 0 ? (
              <Box sx={{ display: 'flex', flexDirection: 'column', gap: 3 }}>
                {note.aiSuggestions.reflectivePrompts.map((item, index) => (
                  <Card key={index} variant="outlined">
                    <CardContent>
                      <Typography variant="subtitle1" fontWeight="medium" gutterBottom>
                        {item.prompt}
                      </Typography>
                      <Box>
                        <TextField
                          fullWidth
                          multiline
                          rows={3}
                          value={localResponses[`reflective_${index}`] || ''}
                          onChange={(e) => handleReflectiveInputChange(index, e.target.value)}
                          placeholder="Write your response here..."
                          variant="outlined"
                          size="small"
                          sx={{ mb: 1 }}
                        />
                        <Box sx={{ display: 'flex', justifyContent: 'flex-end' }}>
                          <Button
                            size="small"
                            variant="outlined"
                            onClick={() => saveReflectiveResponse(index, localResponses[`reflective_${index}`] || '')}
                          >
                            Save Response
                          </Button>
                        </Box>
                      </Box>
                    </CardContent>
                  </Card>
                ))}
              </Box>
            ) : (
              <Typography color="text.secondary">
                Generate reflective prompts to deepen your understanding of this note.
              </Typography>
            )}
          </Box>
        )}

        {/* Concept Rebuilder */}
        {activeTab === 'concept' && !loading && (
          <Box>
            <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
              <Typography variant="h6">
                Concept Rebuilder
              </Typography>
              <Button
                variant="contained"
                onClick={handleGenerateConceptRebuild}
                disabled={loading}
              >
                Simplify Concept
              </Button>
            </Box>

            {note.aiSuggestions?.conceptRebuild?.simplifiedExplanation ? (
              <Box sx={{ display: 'flex', flexDirection: 'column', gap: 3 }}>
                <Paper
                  variant="outlined"
                  sx={{ p: 3, bgcolor: 'primary.50' }}
                >
                  <Typography variant="subtitle1" fontWeight="medium" gutterBottom>
                    Simplified Explanation:
                  </Typography>
                  <Typography variant="body1">
                    {note.aiSuggestions.conceptRebuild.simplifiedExplanation}
                  </Typography>
                </Paper>

                <Box>
                  <Typography variant="subtitle1" fontWeight="medium" gutterBottom>
                    Your Understanding:
                  </Typography>
                  <Box>
                    <TextField
                      fullWidth
                      multiline
                      rows={4}
                      value={localResponses['concept'] || ''}
                      onChange={(e) => handleConceptInputChange(e.target.value)}
                      placeholder="Explain the concept in your own words..."
                      variant="outlined"
                      sx={{ mb: 1 }}
                    />
                    <Box sx={{ display: 'flex', justifyContent: 'flex-end' }}>
                      <Button
                        size="small"
                        variant="outlined"
                        onClick={() => saveConceptResponse(localResponses['concept'] || '')}
                      >
                        Save Response
                      </Button>
                    </Box>
                  </Box>
                </Box>
              </Box>
            ) : (
              <Typography color="text.secondary">
                Get a simplified explanation of this concept if you're finding it difficult to understand.
              </Typography>
            )}
          </Box>
        )}

        {/* Socratic Questions */}
        {activeTab === 'socratic' && !loading && (
          <Box>
            <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
              <Typography variant="h6">
                Socratic Questions
              </Typography>
              <Button
                variant="contained"
                onClick={handleGenerateSocraticQuestions}
                disabled={loading}
              >
                Generate Questions
              </Button>
            </Box>

            {note.aiSuggestions?.socraticQuestions?.length > 0 ? (
              <Box sx={{ display: 'flex', flexDirection: 'column', gap: 3 }}>
                {note.aiSuggestions.socraticQuestions.map((item, index) => (
                  <Card key={index} variant="outlined">
                    <CardContent>
                      <Typography variant="subtitle1" fontWeight="medium" gutterBottom>
                        {item.question}
                      </Typography>
                      <Box>
                        <TextField
                          fullWidth
                          multiline
                          rows={3}
                          value={localResponses[`socratic_${index}`] || ''}
                          onChange={(e) => handleSocraticInputChange(index, e.target.value)}
                          placeholder="Write your response here..."
                          variant="outlined"
                          size="small"
                          sx={{ mb: 1 }}
                        />
                        <Box sx={{ display: 'flex', justifyContent: 'flex-end' }}>
                          <Button
                            size="small"
                            variant="outlined"
                            onClick={() => saveSocraticResponse(index, localResponses[`socratic_${index}`] || '')}
                          >
                            Save Response
                          </Button>
                        </Box>
                      </Box>
                    </CardContent>
                  </Card>
                ))}
              </Box>
            ) : (
              <Typography color="text.secondary">
                Generate Socratic questions to challenge your thinking and deepen your understanding.
              </Typography>
            )}
          </Box>
        )}

        {/* Teach AI */}
        {activeTab === 'teach' && !loading && (
          <Box>
            <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
              <Typography variant="h6">
                Teach the AI
              </Typography>
              {teachStep !== 'initial' && (
                <Button
                  variant="outlined"
                  onClick={handleResetTeachSession}
                  disabled={loading}
                >
                  Start New Session
                </Button>
              )}
            </Box>

            {/* Initial state */}
            {teachStep === 'initial' && (
              <Box sx={{ textAlign: 'center', py: 4 }}>
                <Typography variant="body1" color="text.secondary" sx={{ mb: 2 }}>
                  Reinforce your understanding by teaching this concept to the AI.
                  The AI will ask follow-up questions and provide feedback on your explanation.
                </Typography>
                <Button
                  variant="contained"
                  onClick={handleStartTeachSession}
                  disabled={loading}
                  startIcon={<SchoolIcon />}
                  sx={{ mt: 2 }}
                >
                  Start Teaching Session
                </Button>
              </Box>
            )}

            {/* Explanation phase */}
            {teachStep === 'explanation' && (
              <Box>
                <Typography variant="subtitle1" fontWeight="medium" gutterBottom>
                  Explain the Concept
                </Typography>
                <Typography variant="body2" sx={{ mb: 2 }}>
                  Explain this concept to the AI as if you were teaching it to someone new to the subject.
                </Typography>
                <TextField
                  fullWidth
                  multiline
                  rows={6}
                  value={teachSession.userExplanation}
                  onChange={(e) => setTeachSession(prev => ({ ...prev, userExplanation: e.target.value }))}
                  placeholder="Explain the concept in your own words..."
                  variant="outlined"
                  sx={{ mb: 2 }}
                />
                <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>
                  <Button
                    variant="outlined"
                    onClick={handleResetTeachSession}
                    disabled={loading}
                  >
                    Cancel
                  </Button>
                  <Button
                    variant="contained"
                    onClick={() => handleSubmitExplanation(teachSession.userExplanation)}
                    // disabled={loading || !teachSession.userExplanation.trim()}
                  >
                    Submit Explanation
                  </Button>
                </Box>
              </Box>
            )}

            {/* Questions phase */}
            {teachStep === 'questions' && (
              <Box>
                <Typography variant="subtitle1" fontWeight="medium" gutterBottom>
                  Follow-up Questions
                </Typography>
                <Typography variant="body2" sx={{ mb: 2 }}>
                  The AI is asking questions to deepen your understanding:
                </Typography>

                {/* Question history */}
                {teachSession.questionHistory.length > 0 && (
                  <Box sx={{ mb: 4 }}>
                    <Typography variant="body2" fontWeight="medium" gutterBottom>
                      Previous Questions:
                    </Typography>
                    <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2, mb: 3 }}>
                      {teachSession.questionHistory.map((item, index) => (
                        <Card key={index} variant="outlined" sx={{ bgcolor: 'background.paper' }}>
                          <CardContent>
                            <Typography variant="subtitle2" color="text.secondary" gutterBottom>
                              Question {index + 1}:
                            </Typography>
                            <Typography variant="body2" fontWeight="medium" gutterBottom>
                              {item.question}
                            </Typography>
                            <Typography variant="subtitle2" color="text.secondary" gutterBottom sx={{ mt: 1 }}>
                              Your Response:
                            </Typography>
                            <Typography variant="body2">
                              {item.response}
                            </Typography>
                          </CardContent>
                        </Card>
                      ))}
                    </Box>
                  </Box>
                )}

                {/* Current question */}
                <Box sx={{ mb: 3 }}>
                  <Typography variant="body2" fontWeight="medium" gutterBottom>
                    Current Question:
                  </Typography>
                  <Card variant="outlined" sx={{ bgcolor: 'primary.50', mb: 2 }}>
                    <CardContent>
                      <Typography variant="subtitle1" fontWeight="medium" gutterBottom>
                        {teachSession.followUpQuestions[0]?.question}
                      </Typography>
                      <TextField
                        fullWidth
                        multiline
                        rows={3}
                        value={localResponses[`teach_question_0`] || ''}
                        onChange={(e) => handleTeachQuestionInputChange(0, e.target.value)}
                        placeholder="Write your response here..."
                        variant="outlined"
                        size="small"
                      />
                    </CardContent>
                  </Card>
                </Box>

                <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>
                  <Button
                    variant="outlined"
                    onClick={() => setTeachStep('explanation')}
                    disabled={loading}
                  >
                    Back to Explanation
                  </Button>
                  <Box>
                    <Button
                      variant="outlined"
                      onClick={handleEndTeachSession}
                      disabled={loading || !teachSession.followUpQuestions[0]?.response || teachSession.followUpQuestions[0]?.response.trim() === ''}
                      sx={{ mr: 2 }}
                    >
                      End Session & Get Feedback
                    </Button>
                    <Button
                      variant="contained"
                      onClick={handleSubmitQuestionResponse}
                      disabled={loading || !teachSession.followUpQuestions[0]?.response || teachSession.followUpQuestions[0]?.response.trim() === ''}
                    >
                      Next Question
                    </Button>
                  </Box>
                </Box>
              </Box>
            )}

            {/* Feedback phase */}
            {teachStep === 'feedback' && (
              <Box>
                <Typography variant="subtitle1" fontWeight="medium" gutterBottom>
                  Feedback on Your Understanding
                </Typography>

                <Box sx={{ mb: 4 }}>
                  <Typography variant="body2" fontWeight="medium" gutterBottom>
                    Your Explanation:
                  </Typography>
                  <Paper variant="outlined" sx={{ p: 2, mb: 2, bgcolor: 'background.paper' }}>
                    <Typography variant="body2">
                      {teachSession.userExplanation}
                    </Typography>
                  </Paper>
                </Box>

                <Box sx={{ mb: 4 }}>
                  <Typography variant="body2" fontWeight="medium" gutterBottom>
                    Q&A Session:
                  </Typography>
                  <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2, mb: 3 }}>
                    {teachSession.questionHistory.map((item, index) => (
                      <Card key={index} variant="outlined" sx={{ bgcolor: 'background.paper' }}>
                        <CardContent>
                          <Typography variant="subtitle2" color="text.secondary" gutterBottom>
                            Question {index + 1}:
                          </Typography>
                          <Typography variant="body2" fontWeight="medium" gutterBottom>
                            {item.question}
                          </Typography>
                          <Typography variant="subtitle2" color="text.secondary" gutterBottom sx={{ mt: 1 }}>
                            Your Response:
                          </Typography>
                          <Typography variant="body2">
                            {item.response}
                          </Typography>
                        </CardContent>
                      </Card>
                    ))}
                  </Box>
                </Box>

                <Box sx={{ mb: 4 }}>
                  <Typography variant="body2" fontWeight="medium" gutterBottom>
                    Strengths:
                  </Typography>
                  <List dense>
                    {teachSession.feedback.strengths.map((strength, index) => (
                      <ListItem key={index}>
                        <ListItemIcon>
                          <CheckCircleOutline color="success" />
                        </ListItemIcon>
                        <ListItemText primary={strength} />
                      </ListItem>
                    ))}
                  </List>
                </Box>

                <Box sx={{ mb: 4 }}>
                  <Typography variant="body2" fontWeight="medium" gutterBottom>
                    Areas for Improvement:
                  </Typography>
                  <List dense>
                    {teachSession.feedback.improvements.map((improvement, index) => (
                      <ListItem key={index}>
                        <ListItemIcon>
                          <InfoOutlined color="info" />
                        </ListItemIcon>
                        <ListItemText primary={improvement} />
                      </ListItem>
                    ))}
                  </List>
                </Box>

                {teachSession.feedback.suggestedRevision && (
                  <Box sx={{ mb: 4 }}>
                    <Typography variant="body2" fontWeight="medium" gutterBottom>
                      Suggested Improved Explanation:
                    </Typography>
                    <Paper variant="outlined" sx={{ p: 2, bgcolor: 'background.paper' }}>
                      <Typography variant="body2">
                        {teachSession.feedback.suggestedRevision}
                      </Typography>
                    </Paper>
                  </Box>
                )}

                <Box sx={{ mb: 4 }}>
                  <Typography variant="body2" fontWeight="medium" gutterBottom>
                    Confidence Score: {teachSession.feedback.confidenceScore}/100
                  </Typography>
                  <LinearProgress
                    variant="determinate"
                    value={teachSession.feedback.confidenceScore}
                    sx={{
                      height: 10,
                      borderRadius: 5,
                      bgcolor: 'grey.200',
                      '& .MuiLinearProgress-bar': {
                        bgcolor: teachSession.feedback.confidenceScore > 80 ? 'success.main' :
                          teachSession.feedback.confidenceScore > 50 ? 'warning.main' : 'error.main',
                      }
                    }}
                  />
                </Box>

                {teachSession.summary && (
                  <Box sx={{ mb: 4 }}>
                    <Typography variant="body2" fontWeight="medium" gutterBottom>
                      Summary:
                    </Typography>
                    <Paper variant="outlined" sx={{ p: 2, bgcolor: 'background.paper' }}>
                      <Typography variant="body2">
                        {teachSession.summary}
                      </Typography>
                    </Paper>
                  </Box>
                )}

                <Box sx={{ display: 'flex', justifyContent: 'space-between', flexWrap: 'wrap', gap: 2 }}>
                  <Button
                    variant="outlined"
                    onClick={() => setTeachStep('questions')}
                    disabled={loading}
                  >
                    Back to Questions
                  </Button>

                  <Box sx={{ display: 'flex', gap: 2 }}>
                    {/* Show Create Roadmap button only if confidence score is below 80 */}
                    {teachSession.feedback.confidenceScore < 80 && (
                      <Button
                        variant="contained"
                        onClick={handleCreateRoadmap}
                        disabled={loading}
                        color="secondary"
                        startIcon={<SchoolIcon />}
                      >
                        Create Learning Roadmap
                      </Button>
                    )}

                    <Button
                      variant="contained"
                      onClick={handleResetTeachSession}
                      disabled={loading}
                      color="primary"
                    >
                      Start New Session
                    </Button>
                  </Box>
                </Box>
              </Box>
            )}
          </Box>
        )}
      </Box>
    </Paper>
  );
};

export default AiFeatures;
