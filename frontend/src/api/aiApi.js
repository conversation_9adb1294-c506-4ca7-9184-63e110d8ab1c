import axiosInstance from './axios';

// Generate reflective prompts
export const generateReflectivePrompts = async (noteContent) => {
  try {
    const response = await axiosInstance.post('/ai/reflective-prompts', { noteContent });
    return response.data;
  } catch (error) {
    throw error.response?.data?.message || 'An error occurred while generating reflective prompts';
  }
};

// Generate concept rebuild (simplified explanation)
export const generateConceptRebuild = async (noteContent) => {
  try {
    const response = await axiosInstance.post('/ai/concept-rebuild', { noteContent });
    return response.data;
  } catch (error) {
    throw error.response?.data?.message || 'An error occurred while generating simplified explanation';
  }
};

// Generate Socratic questions
export const generateSocraticQuestions = async (noteContent) => {
  try {
    const response = await axiosInstance.post('/ai/socratic-questions', { noteContent });
    return response.data;
  } catch (error) {
    throw error.response?.data?.message || 'An error occurred while generating Socratic questions';
  }
};

// Extract tags from note content
export const extractTags = async (noteContent) => {
  try {
    const response = await axiosInstance.post('/ai/extract-tags', { noteContent });
    return response.data;
  } catch (error) {
    throw error.response?.data?.message || 'An error occurred while extracting tags';
  }
};

// Start a teach AI session
export const startTeachSession = async (noteContent) => {
  try {
    const response = await axiosInstance.post('/ai/teach-ai/start', { noteContent });
    return response.data;
  } catch (error) {
    throw error.response?.data?.message || 'An error occurred while starting teach session';
  }
};

// Submit user explanation and get follow-up questions
export const submitExplanation = async (noteContent, userExplanation) => {
  try {
    const response = await axiosInstance.post('/ai/teach-ai/explain', { noteContent, userExplanation });
    return response.data;
  } catch (error) {
    throw error.response?.data?.message || 'An error occurred while submitting explanation';
  }
};

// Submit response to a question and get the next question
export const submitQuestionResponse = async (noteContent, userExplanation, currentQuestion, questionResponse, questionHistory = []) => {
  try {
    const response = await axiosInstance.post('/ai/teach-ai/next-question', {
      noteContent,
      userExplanation,
      currentQuestion,
      questionResponse,
      questionHistory
    });
    return response.data;
  } catch (error) {
    throw error.response?.data?.message || 'An error occurred while generating the next question';
  }
};

// Submit responses to follow-up questions and get feedback
export const generateFeedback = async (noteContent, userExplanation, questionHistory) => {
  try {
    const response = await axiosInstance.post('/ai/teach-ai/feedback', { noteContent, userExplanation, questionHistory });
    return response.data;
  } catch (error) {
    throw error.response?.data?.message || 'An error occurred while generating feedback';
  }
};

// Generate a learning roadmap based on feedback
export const generateRoadmap = async (noteContent, userExplanation, questionHistory, feedback) => {
  try {
    const response = await axiosInstance.post('/ai/generate-roadmap', {
      noteContent,
      userExplanation,
      questionHistory,
      feedback
    });
    return response.data;
  } catch (error) {
    throw error.response?.data?.message || 'An error occurred while generating the roadmap';
  }
};
