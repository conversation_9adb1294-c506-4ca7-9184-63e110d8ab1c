import axiosInstance from './axios';

// Register a new user
export const registerUser = async (userData) => {
  try {
    const response = await axiosInstance.post('/users', userData);
    return response.data;
  } catch (error) {
    throw error.response?.data?.message || 'An error occurred during registration';
  }
};

// Login a user
export const loginUser = async (userData) => {
  try {
    console.log('Attempting to login with:', userData);
    console.log('Using API URL:', axiosInstance.defaults.baseURL);
    const response = await axiosInstance.post('/users/login', userData);
    console.log('Login response:', response.data);
    return response.data;
  } catch (error) {
    console.error('Login error:', error);
    throw error.response?.data?.message || 'An error occurred during login';
  }
};

// Get user profile
export const getUserProfile = async () => {
  try {
    const response = await axiosInstance.get('/users/profile');
    return response.data;
  } catch (error) {
    throw error.response?.data?.message || 'An error occurred while fetching profile';
  }
};
