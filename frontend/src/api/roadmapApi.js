import axiosInstance from './axios';

// Create a new roadmap
export const createRoadmap = async (noteId, roadmapData) => {
  try {
    const response = await axiosInstance.post('/roadmaps', { noteId, roadmapData });
    return response.data;
  } catch (error) {
    throw error.response?.data?.message || 'An error occurred while creating the roadmap';
  }
};

// Get all roadmaps
export const getRoadmaps = async () => {
  try {
    const response = await axiosInstance.get('/roadmaps');
    return response.data;
  } catch (error) {
    throw error.response?.data?.message || 'An error occurred while fetching roadmaps';
  }
};

// Get roadmap stats
export const getRoadmapStats = async () => {
  try {
    const roadmaps = await getRoadmaps();

    // Calculate stats
    const totalRoadmaps = roadmaps.length;
    const completedRoadmaps = roadmaps.filter(roadmap => roadmap.completed).length;
    const inProgressRoadmaps = totalRoadmaps - completedRoadmaps;

    // Calculate completion percentage
    const completionPercentage = totalRoadmaps > 0 ? Math.round((completedRoadmaps / totalRoadmaps) * 100) : 0;

    // Get upcoming due modules (modules that are not completed)
    const upcomingModules = [];

    roadmaps.forEach(roadmap => {
      if (!roadmap.completed) {
        roadmap.modules.forEach(module => {
          if (!module.completed) {
            upcomingModules.push({
              roadmapId: roadmap._id,
              roadmapTitle: roadmap.title,
              moduleId: module._id,
              moduleTitle: module.title,
              timeframe: module.timeframe
            });
          }
        });
      }
    });

    // Sort upcoming modules by timeframe (assuming timeframe starts with "Week X")
    upcomingModules.sort((a, b) => {
      const weekA = parseInt(a.timeframe.match(/Week (\d+)/) ? a.timeframe.match(/Week (\d+)/)[1] : '999');
      const weekB = parseInt(b.timeframe.match(/Week (\d+)/) ? b.timeframe.match(/Week (\d+)/)[1] : '999');
      return weekA - weekB;
    });

    return {
      totalRoadmaps,
      completedRoadmaps,
      inProgressRoadmaps,
      completionPercentage,
      upcomingModules: upcomingModules.slice(0, 5), // Get top 5 upcoming modules
      roadmaps
    };
  } catch (error) {
    throw error.response?.data?.message || 'An error occurred while fetching roadmap stats';
  }
};

// Get a roadmap by ID
export const getRoadmapById = async (id) => {
  try {
    const response = await axiosInstance.get(`/roadmaps/${id}`);
    return response.data;
  } catch (error) {
    throw error.response?.data?.message || 'An error occurred while fetching the roadmap';
  }
};

// Update a roadmap
export const updateRoadmap = async (id, roadmapData) => {
  try {
    const response = await axiosInstance.put(`/roadmaps/${id}`, roadmapData);
    return response.data;
  } catch (error) {
    throw error.response?.data?.message || 'An error occurred while updating the roadmap';
  }
};

// Update a module's completion status
export const updateModuleStatus = async (roadmapId, moduleId, completed) => {
  try {
    const response = await axiosInstance.put(`/roadmaps/${roadmapId}/modules/${moduleId}`, { completed });
    return response.data;
  } catch (error) {
    throw error.response?.data?.message || 'An error occurred while updating the module status';
  }
};

// Delete a roadmap
export const deleteRoadmap = async (id) => {
  try {
    const response = await axiosInstance.delete(`/roadmaps/${id}`);
    return response.data;
  } catch (error) {
    throw error.response?.data?.message || 'An error occurred while deleting the roadmap';
  }
};
