import axiosInstance from './axios';

// Get all notes
export const getNotes = async () => {
  try {
    const response = await axiosInstance.get('/notes');
    return response.data;
  } catch (error) {
    throw error.response?.data?.message || 'An error occurred while fetching notes';
  }
};

// Get dashboard stats
export const getDashboardStats = async () => {
  try {
    const notes = await getNotes();

    // Calculate stats from notes
    const totalNotes = notes.length;
    const recentNotes = notes.sort((a, b) => new Date(b.updatedAt) - new Date(a.updatedAt)).slice(0, 5);

    // Calculate average confidence score
    let totalConfidenceScore = 0;
    let notesWithScore = 0;

    notes.forEach(note => {
      if (note.suggestions?.teachSession?.feedback?.confidenceScore) {
        totalConfidenceScore += note.suggestions.teachSession.feedback.confidenceScore;
        notesWithScore++;
      }
    });

    const averageConfidence = notesWithScore > 0 ? Math.round(totalConfidenceScore / notesWithScore) : 0;

    // Get notes with low confidence scores (below 80)
    const lowConfidenceNotes = notes.filter(note =>
      note.suggestions?.teachSession?.feedback?.confidenceScore &&
      note.suggestions.teachSession.feedback.confidenceScore < 80
    );

    return {
      totalNotes,
      recentNotes,
      averageConfidence,
      lowConfidenceNotes,
      notes
    };
  } catch (error) {
    throw error.response?.data?.message || 'An error occurred while fetching dashboard stats';
  }
};

// Get a single note by ID
export const getNoteById = async (noteId) => {
  try {
    const response = await axiosInstance.get(`/notes/${noteId}`);
    return response.data;
  } catch (error) {
    throw error.response?.data?.message || 'An error occurred while fetching the note';
  }
};

// Create a new note
export const createNote = async (noteData) => {
  try {
    const response = await axiosInstance.post('/notes', noteData);
    return response.data;
  } catch (error) {
    throw error.response?.data?.message || 'An error occurred while creating the note';
  }
};

// Update a note
export const updateNote = async (noteId, noteData) => {
  try {
    const response = await axiosInstance.put(`/notes/${noteId}`, noteData);
    return response.data;
  } catch (error) {
    throw error.response?.data?.message || 'An error occurred while updating the note';
  }
};

// Delete a note
export const deleteNote = async (noteId) => {
  try {
    const response = await axiosInstance.delete(`/notes/${noteId}`);
    return response.data;
  } catch (error) {
    throw error.response?.data?.message || 'An error occurred while deleting the note';
  }
};

// Update AI suggestions for a note
export const updateAiSuggestions = async (noteId, aiSuggestions) => {
  try {
    const response = await axiosInstance.put(`/notes/${noteId}/ai-suggestions`, aiSuggestions);
    return response.data;
  } catch (error) {
    throw error.response?.data?.message || 'An error occurred while updating AI suggestions';
  }
};
