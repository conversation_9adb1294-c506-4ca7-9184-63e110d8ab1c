import { useContext, useEffect, useState } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { NoteContext } from '../context/NoteContext';
import NoteEditor from '../components/NoteEditor';
import { getNoteById } from '../api/noteApi';
import {
  Container,
  Typography,
  Alert,
  Box,
  Button,
  CircularProgress
} from '@mui/material';

const EditNotePage = () => {
  const { id } = useParams();
  const { editNote, loading, error } = useContext(NoteContext);
  const [note, setNote] = useState(null);
  const [fetchLoading, setFetchLoading] = useState(true);
  const [fetchError, setFetchError] = useState(null);
  const navigate = useNavigate();

  useEffect(() => {
    const fetchNote = async () => {
      try {
        const data = await getNoteById(id);
        setNote(data);
      } catch (error) {
        setFetchError(error.toString());
        console.error('Error fetching note:', error);
      } finally {
        setFetchLoading(false);
      }
    };

    fetchNote();
  }, [id]);

  const handleSave = async (noteData) => {
    try {
      await editNote(id, noteData);
      navigate(`/notes/${id}`);
    } catch (error) {
      console.error('Error updating note:', error);
    }
  };

  if (fetchLoading) {
    return (
      <Container maxWidth="lg" sx={{ py: 4, textAlign: 'center' }}>
        <CircularProgress size={48} />
        <Typography variant="body1" color="text.secondary" sx={{ mt: 2 }}>
          Loading note...
        </Typography>
      </Container>
    );
  }

  if (fetchError) {
    return (
      <Container maxWidth="lg" sx={{ py: 4 }}>
        <Alert severity="error" sx={{ mb: 3 }}>
          {fetchError}
        </Alert>
        <Button
          variant="contained"
          onClick={() => navigate('/dashboard')}
          sx={{ mt: 2 }}
        >
          Back to Dashboard
        </Button>
      </Container>
    );
  }

  return (
    <Container maxWidth="lg" sx={{ py: 4 }}>
      <Typography variant="h4" component="h1" fontWeight="bold" color="text.primary" gutterBottom>
        Edit Note
      </Typography>

      {error && (
        <Alert severity="error" sx={{ mb: 3 }}>
          {error}
        </Alert>
      )}

      <Box sx={{ mt: 3 }}>
        {note && <NoteEditor note={note} onSave={handleSave} />}
      </Box>
    </Container>
  );
};

export default EditNotePage;
