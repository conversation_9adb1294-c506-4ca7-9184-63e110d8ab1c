import { useContext } from 'react';
import { useNavigate } from 'react-router-dom';
import { NoteContext } from '../context/NoteContext';
import NoteEditor from '../components/NoteEditor';
import { Container, Typography, Alert, Box } from '@mui/material';

const NewNotePage = () => {
  const { addNote, loading, error } = useContext(NoteContext);
  const navigate = useNavigate();

  const handleSave = async (noteData) => {
    try {
      const newNote = await addNote(noteData);
      navigate(`/notes/${newNote._id}`);
    } catch (error) {
      console.error('Error creating note:', error);
    }
  };

  return (
    <Container maxWidth="lg" sx={{ py: 4 }}>
      <Typography variant="h4" component="h1" fontWeight="bold" color="text.primary" gutterBottom>
        Create New Note
      </Typography>

      {error && (
        <Alert severity="error" sx={{ mb: 3 }}>
          {error}
        </Alert>
      )}

      <Box sx={{ mt: 3 }}>
        <NoteEditor onSave={handleSave} isNew={true} />
      </Box>
    </Container>
  );
};

export default NewNotePage;
