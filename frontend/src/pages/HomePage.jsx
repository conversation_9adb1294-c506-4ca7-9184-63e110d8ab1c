import { Link as RouterLink } from 'react-router-dom';
import {
  Box,
  <PERSON><PERSON>,
  Card,
  CardContent,
  Container,
  Grid,
  Typography,
} from '@mui/material';

const HomePage = () => {
  // const theme = useTheme();

  return (
    <Box
      sx={{
        minHeight: '100vh',
        background: 'linear-gradient(to bottom, #e0e7ff, #ffffff)',
        pt: 8,
        pb: 8,
      }}
    >
      <Container maxWidth="lg">
        <Box sx={{ maxWidth: 800, mx: 'auto', textAlign: 'center', mb: 10 }}>
          <Typography
            variant="h1"
            component="h1"
            color="primary.dark"
            gutterBottom
            sx={{ fontSize: { xs: '2rem', md: '2.5rem' } }}
          >
            AI-Driven Personalized Note-Taking
          </Typography>
          <Typography
            variant="h5"
            color="text.secondary"
            paragraph
            sx={{ mb: 4 }}
          >
            Enhance your learning with AI-powered reflective prompts, concept simplification,
            and Socratic questioning. Take smarter notes and retain information better.
          </Typography>
          <Box sx={{ display: 'flex', justifyContent: 'center', gap: 2 }}>
            <Button
              variant="contained"
              component={RouterLink}
              to="/register"
              size="large"
            >
              Get Started
            </Button>
            <Button
              variant="outlined"
              component={RouterLink}
              to="/login"
              size="large"
            >
              Login
            </Button>
          </Box>
        </Box>

        <Grid container spacing={4} sx={{ mb: 10 }}>
          <Grid item xs={12} md={4}>
            <Card sx={{ height: '100%' }}>
              <CardContent>
                <Typography
                  variant="h4"
                  component="h2"
                  color="primary.dark"
                  gutterBottom
                >
                  Reflective Prompting
                </Typography>
                <Typography variant="body1" color="text.secondary">
                  After taking notes, receive thoughtful prompts that encourage deeper understanding and
                  active recall of the material.
                </Typography>
              </CardContent>
            </Card>
          </Grid>
          <Grid item xs={12} md={4}>
            <Card sx={{ height: '100%' }}>
              <CardContent>
                <Typography
                  variant="h4"
                  component="h2"
                  color="primary.dark"
                  gutterBottom
                >
                  Concept Rebuilder
                </Typography>
                <Typography variant="body1" color="text.secondary">
                  Struggling with a complex concept? Get simplified explanations and analogies to help
                  you understand difficult ideas.
                </Typography>
              </CardContent>
            </Card>
          </Grid>
          <Grid item xs={12} md={4}>
            <Card sx={{ height: '100%' }}>
              <CardContent>
                <Typography
                  variant="h4"
                  component="h2"
                  color="primary.dark"
                  gutterBottom
                >
                  Socratic Questioning
                </Typography>
                <Typography variant="body1" color="text.secondary">
                  Engage with AI-generated Socratic questions that challenge your thinking and promote
                  critical analysis of your notes.
                </Typography>
              </CardContent>
            </Card>
          </Grid>
        </Grid>

        <Box sx={{ textAlign: 'center', maxWidth: 800, mx: 'auto' }}>
          <Typography
            variant="h2"
            component="h2"
            color="primary.dark"
            gutterBottom
          >
            Transform Your Learning Experience
          </Typography>
          <Typography
            variant="h5"
            color="text.secondary"
            paragraph
            sx={{ mb: 4 }}
          >
            Our AI-powered note-taking app helps you not just collect information, but truly understand
            and retain it through active engagement and personalized learning.
          </Typography>
          <Button
            variant="contained"
            component={RouterLink}
            to="/register"
            size="large"
          >
            Start Taking Smarter Notes
          </Button>
        </Box>
      </Container>
    </Box>
  );
};

export default HomePage;
