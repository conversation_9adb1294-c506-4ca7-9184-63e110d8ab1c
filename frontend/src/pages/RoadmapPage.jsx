import { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { getRoadmapById, updateModuleStatus, deleteRoadmap } from '../api/roadmapApi';
import {
  Box,
  Container,
  Typography,
  Paper,
  Divider,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
  Checkbox,
  Button,
  Chip,
  Card,
  CardContent,
  CardActions,
  Alert,
  CircularProgress,
  Dialog,
  DialogActions,
  DialogContent,
  DialogContentText,
  DialogTitle,
  IconButton,
  Tooltip,
} from '@mui/material';
import {
  CheckCircle as CheckCircleIcon,
  ArrowBack as ArrowBackIcon,
  Delete as DeleteIcon,
  Edit as EditIcon,
  School as SchoolIcon,
  MenuBook as MenuBookIcon,
  Assignment as AssignmentIcon,
  Timer as TimerIcon,
  Flag as FlagIcon,
} from '@mui/icons-material';

const RoadmapPage = () => {
  const { id } = useParams();
  const navigate = useNavigate();
  const [roadmap, setRoadmap] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);

  useEffect(() => {
    const fetchRoadmap = async () => {
      try {
        const data = await getRoadmapById(id);
        setRoadmap(data);
      } catch (error) {
        setError('Failed to load roadmap');
        console.error(error);
      } finally {
        setLoading(false);
      }
    };

    fetchRoadmap();
  }, [id]);

  const handleModuleStatusChange = async (moduleId, completed) => {
    try {
      const updatedRoadmap = await updateModuleStatus(id, moduleId, completed);
      setRoadmap(updatedRoadmap);
    } catch (error) {
      setError('Failed to update module status');
      console.error(error);
    }
  };

  const handleDeleteRoadmap = async () => {
    try {
      await deleteRoadmap(id);
      navigate('/roadmaps');
    } catch (error) {
      setError('Failed to delete roadmap');
      console.error(error);
    }
  };

  if (loading) {
    return (
      <Container maxWidth="md" sx={{ py: 4 }}>
        <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '50vh' }}>
          <CircularProgress />
        </Box>
      </Container>
    );
  }

  if (error) {
    return (
      <Container maxWidth="md" sx={{ py: 4 }}>
        <Alert severity="error" sx={{ mb: 2 }}>
          {error}
        </Alert>
        <Button startIcon={<ArrowBackIcon />} onClick={() => navigate('/roadmaps')}>
          Back to Roadmaps
        </Button>
      </Container>
    );
  }

  if (!roadmap) {
    return (
      <Container maxWidth="md" sx={{ py: 4 }}>
        <Alert severity="info">Roadmap not found</Alert>
        <Button startIcon={<ArrowBackIcon />} onClick={() => navigate('/roadmaps')} sx={{ mt: 2 }}>
          Back to Roadmaps
        </Button>
      </Container>
    );
  }

  // If there was a parse error, display the raw content
  if (roadmap.parseError) {
    return (
      <Container maxWidth="md" sx={{ py: 4 }}>
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
          <Button startIcon={<ArrowBackIcon />} onClick={() => navigate('/roadmaps')}>
            Back to Roadmaps
          </Button>
          <Typography variant="h4" component="h1">
            Learning Roadmap
          </Typography>
          <IconButton color="error" onClick={() => setDeleteDialogOpen(true)}>
            <DeleteIcon />
          </IconButton>
        </Box>
        <Paper sx={{ p: 3, mb: 3 }}>
          <Typography variant="body1" component="pre" sx={{ whiteSpace: 'pre-wrap' }}>
            {roadmap.rawContent}
          </Typography>
        </Paper>

        {/* Delete Confirmation Dialog */}
        <Dialog open={deleteDialogOpen} onClose={() => setDeleteDialogOpen(false)}>
          <DialogTitle>Delete Roadmap</DialogTitle>
          <DialogContent>
            <DialogContentText>
              Are you sure you want to delete this roadmap? This action cannot be undone.
            </DialogContentText>
          </DialogContent>
          <DialogActions>
            <Button onClick={() => setDeleteDialogOpen(false)}>Cancel</Button>
            <Button onClick={handleDeleteRoadmap} color="error">
              Delete
            </Button>
          </DialogActions>
        </Dialog>
      </Container>
    );
  }

  return (
    <Container maxWidth="md" sx={{ py: 4 }}>
      {/* Header */}
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
        <Button startIcon={<ArrowBackIcon />} onClick={() => navigate('/roadmaps')}>
          Back to Roadmaps
        </Button>
        <Box sx={{ display: 'flex', alignItems: 'center' }}>
          <SchoolIcon color="primary" sx={{ mr: 1 }} />
          <Typography variant="h5" component="h1">
            {roadmap.title}
          </Typography>
        </Box>
        <Box>
          <Tooltip title="Delete Roadmap">
            <IconButton color="error" onClick={() => setDeleteDialogOpen(true)}>
              <DeleteIcon />
            </IconButton>
          </Tooltip>
        </Box>
      </Box>

      {/* Overview */}
      <Paper sx={{ p: 3, mb: 3 }}>
        <Typography variant="h6" gutterBottom>
          Overview
        </Typography>
        <Typography variant="body1" paragraph>
          {roadmap.overview}
        </Typography>

        {/* Knowledge Gaps */}
        {roadmap.knowledgeGaps && roadmap.knowledgeGaps.length > 0 && (
          <Box sx={{ mt: 3 }}>
            <Typography variant="subtitle1" fontWeight="bold" gutterBottom>
              Knowledge Gaps to Address:
            </Typography>
            <List dense>
              {roadmap.knowledgeGaps.map((gap, index) => (
                <ListItem key={index}>
                  <ListItemIcon>
                    <FlagIcon color="warning" />
                  </ListItemIcon>
                  <ListItemText primary={gap} />
                </ListItem>
              ))}
            </List>
          </Box>
        )}

        {/* Progress */}
        <Box sx={{ mt: 3, display: 'flex', alignItems: 'center' }}>
          <Typography variant="subtitle1" fontWeight="bold" sx={{ mr: 2 }}>
            Overall Progress:
          </Typography>
          <Chip
            label={roadmap.completed ? 'Completed' : 'In Progress'}
            color={roadmap.completed ? 'success' : 'primary'}
            icon={roadmap.completed ? <CheckCircleIcon /> : null}
          />
        </Box>
      </Paper>

      {/* Learning Modules */}
      <Typography variant="h6" gutterBottom>
        Learning Modules
      </Typography>
      <Box sx={{ mb: 4 }}>
        {roadmap.modules.map((module, index) => (
          <Card key={index} variant="outlined" sx={{ mb: 2, bgcolor: module.completed ? 'success.50' : 'background.paper' }}>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                <Checkbox
                  checked={module.completed}
                  onChange={(e) => handleModuleStatusChange(module._id, e.target.checked)}
                  color="success"
                />
                <Typography variant="h6" component="div">
                  {module.title}
                </Typography>
              </Box>

              {/* Timeframe */}
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                <TimerIcon fontSize="small" color="action" sx={{ mr: 1 }} />
                <Typography variant="body2" color="text.secondary">
                  {module.timeframe}
                </Typography>
              </Box>

              {/* Objectives */}
              {module.objectives && module.objectives.length > 0 && (
                <Box sx={{ mb: 2 }}>
                  <Typography variant="subtitle2" fontWeight="bold" gutterBottom>
                    Learning Objectives:
                  </Typography>
                  <List dense>
                    {module.objectives.map((objective, idx) => (
                      <ListItem key={idx} sx={{ py: 0 }}>
                        <ListItemText primary={objective} />
                      </ListItem>
                    ))}
                  </List>
                </Box>
              )}

              {/* Resources */}
              {module.resources && module.resources.length > 0 && (
                <Box sx={{ mb: 2 }}>
                  <Typography variant="subtitle2" fontWeight="bold" gutterBottom>
                    Resources:
                  </Typography>
                  <List dense>
                    {module.resources.map((resource, idx) => (
                      <ListItem key={idx} sx={{ py: 0 }}>
                        <ListItemIcon sx={{ minWidth: 36 }}>
                          <MenuBookIcon fontSize="small" color="primary" />
                        </ListItemIcon>
                        <ListItemText primary={resource} />
                      </ListItem>
                    ))}
                  </List>
                </Box>
              )}

              {/* Exercises */}
              {module.exercises && module.exercises.length > 0 && (
                <Box>
                  <Typography variant="subtitle2" fontWeight="bold" gutterBottom>
                    Practice Exercises:
                  </Typography>
                  <List dense>
                    {module.exercises.map((exercise, idx) => (
                      <ListItem key={idx} sx={{ py: 0 }}>
                        <ListItemIcon sx={{ minWidth: 36 }}>
                          <AssignmentIcon fontSize="small" color="secondary" />
                        </ListItemIcon>
                        <ListItemText primary={exercise} />
                      </ListItem>
                    ))}
                  </List>
                </Box>
              )}
            </CardContent>
            <CardActions sx={{ justifyContent: 'flex-end', p: 2, pt: 0 }}>
              <Button
                size="small"
                variant="outlined"
                color={module.completed ? 'success' : 'primary'}
                onClick={() => handleModuleStatusChange(module._id, !module.completed)}
              >
                {module.completed ? 'Mark as Incomplete' : 'Mark as Complete'}
              </Button>
            </CardActions>
          </Card>
        ))}
      </Box>

      {/* Final Assessment */}
      {roadmap.assessment && (
        <Paper sx={{ p: 3, mb: 3 }}>
          <Typography variant="h6" gutterBottom>
            Final Assessment
          </Typography>
          <Typography variant="body1">{roadmap.assessment}</Typography>
        </Paper>
      )}

      {/* Delete Confirmation Dialog */}
      <Dialog open={deleteDialogOpen} onClose={() => setDeleteDialogOpen(false)}>
        <DialogTitle>Delete Roadmap</DialogTitle>
        <DialogContent>
          <DialogContentText>
            Are you sure you want to delete this roadmap? This action cannot be undone.
          </DialogContentText>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setDeleteDialogOpen(false)}>Cancel</Button>
          <Button onClick={handleDeleteRoadmap} color="error">
            Delete
          </Button>
        </DialogActions>
      </Dialog>
    </Container>
  );
};

export default RoadmapPage;
