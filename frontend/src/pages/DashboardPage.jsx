import { useContext, useEffect, useState } from 'react';
import { Link as RouterLink } from 'react-router-dom';
import { NoteContext } from '../context/NoteContext';
import NoteCard from '../components/NoteCard';
import NoteCardSkeleton from '../components/NoteCardSkeleton';
import {
  Box,
  Button,
  CircularProgress,
  Container,
  FormControl,
  Grid,
  InputAdornment,
  MenuItem,
  Paper,
  Select,
  TextField,
  Typography,
  Alert,
} from '@mui/material';
import { Add, Search } from '@mui/icons-material';

const DashboardPage = () => {
  const { notes, loading, error, fetchNotes, removeNote } = useContext(NoteContext);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedTag, setSelectedTag] = useState('');

  useEffect(() => {
    // Only fetch notes once when the component mounts
    fetchNotes(false);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  // Filter notes based on search term and selected tag
  const filteredNotes = notes.filter((note) => {
    const matchesSearch = searchTerm
      ? note.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
      note.content.toLowerCase().includes(searchTerm.toLowerCase())
      : true;

    const matchesTag = selectedTag
      ? note.tags.includes(selectedTag)
      : true;

    return matchesSearch && matchesTag;
  });

  // Get all unique tags from notes
  const allTags = [...new Set(notes.flatMap((note) => note.tags))].sort();

  return (
    <Container maxWidth="lg" sx={{ py: 4 }}>
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 4 }}>
        <Typography variant="h4" component="h1" fontWeight="bold" color="text.primary">
          Your Notes
        </Typography>
        <Button
          variant="contained"
          component={RouterLink}
          to="/notes/new"
          startIcon={<Add />}
        >
          New Note
        </Button>
      </Box>

      {/* Search and filter */}
      <Box
        sx={{
          mb: 4,
          display: 'flex',
          flexDirection: { xs: 'column', md: 'row' },
          gap: 2,
          alignItems: { md: 'center' }
        }}
      >
        <TextField
          fullWidth
          placeholder="Search notes..."
          value={searchTerm}
          onChange={(e) => setSearchTerm(e.target.value)}
          InputProps={{
            startAdornment: (
              <InputAdornment position="start">
                <Search color="action" />
              </InputAdornment>
            ),
          }}
          sx={{ mb: { xs: 2, md: 0 } }}
        />
        <FormControl sx={{ minWidth: { xs: '100%', md: 200 } }}>
          <Select
            value={selectedTag}
            onChange={(e) => setSelectedTag(e.target.value)}
            displayEmpty
          >
            <MenuItem value="">All Tags</MenuItem>
            {allTags.map((tag) => (
              <MenuItem key={tag} value={tag}>
                #{tag}
              </MenuItem>
            ))}
          </Select>
        </FormControl>
      </Box>

      {/* Error message */}
      {error && (
        <Alert severity="error" sx={{ mb: 3 }}>
          {error}
        </Alert>
      )}

      {/* Loading indicator at the top */}
      {loading && (
        <Box sx={{ display: 'flex', justifyContent: 'center', mb: 3 }}>
          <CircularProgress size={24} thickness={4} />
        </Box>
      )}

      {/* No notes state */}
      {!loading && notes.length > 0 && filteredNotes.length === 0 && (
        <Paper sx={{ textAlign: 'center', py: 6, px: 4, bgcolor: 'background.paper' }}>
          <Typography variant="h5" component="h3" gutterBottom>
            No matching notes
          </Typography>
          <Typography variant="body1" color="text.secondary">
            Try adjusting your search or filter
          </Typography>
        </Paper>
      )}

      {/* Empty state */}
      {!loading && notes.length === 0 && (
        <Paper sx={{ textAlign: 'center', py: 6, px: 4, bgcolor: 'background.paper' }}>
          <Typography variant="h5" component="h3" gutterBottom>
            No notes yet
          </Typography>
          <Typography variant="body1" color="text.secondary" paragraph>
            Create your first note to get started
          </Typography>
          <Button
            variant="contained"
            component={RouterLink}
            to="/notes/new"
            sx={{ mt: 2 }}
          >
            Create Note
          </Button>
        </Paper>
      )}

      {/* Notes grid with skeletons during loading */}
      <Grid container spacing={3}>
        {loading ? (
          // Show skeletons while loading
          Array.from(new Array(6)).map((_, index) => (
            <Grid item xs={12} sm={6} md={4} key={`skeleton-${index}`}>
              <NoteCardSkeleton />
            </Grid>
          ))
        ) : filteredNotes.length > 0 ? (
          // Show actual notes
          filteredNotes.map((note) => (
            <Grid item xs={12} sm={6} md={4} key={note._id}>
              <NoteCard note={note} onDelete={removeNote} />
            </Grid>
          ))
        ) : null}
      </Grid>
    </Container>
  );
};

export default DashboardPage;
