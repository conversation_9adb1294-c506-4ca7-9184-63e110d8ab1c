import { useContext, useEffect, useState } from 'react';
import { useParams, useNavigate, Link as RouterLink } from 'react-router-dom';
import { NoteContext } from '../context/NoteContext';
import AiFeatures from '../components/AiFeatures';
import ScoreIndicatorGroup from '../components/ScoreIndicatorGroup';
import { getNoteById } from '../api/noteApi';
import { calculateFeatureScores } from '../utils/scoreUtils';
import {
  Container,
  Typography,
  Box,
  Paper,
  Chip,
  Button,
  IconButton,
  CircularProgress,
  Alert,
  Dialog,
  DialogActions,
  DialogContent,
  DialogContentText,
  DialogTitle,
  Divider,
  useTheme,
} from '@mui/material';
import {
  Edit as EditIcon,
  Delete as DeleteIcon,
  ArrowBack as ArrowBackIcon,
} from '@mui/icons-material';

const NoteDetailPage = () => {
  const { id } = useParams();
  const { removeNote } = useContext(NoteContext);
  const [note, setNote] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [openDialog, setOpenDialog] = useState(false);
  const [featureScores, setFeatureScores] = useState({
    reflective: null,
    concept: null,
    socratic: null,
    teach: null
  });
  const navigate = useNavigate();
  const theme = useTheme();

  // Function to fetch note data
  const fetchNote = async () => {
    setLoading(true);
    try {
      const data = await getNoteById(id);

      // Initialize aiSuggestions if it doesn't exist
      if (!data.aiSuggestions) {
        data.aiSuggestions = {
          reflectivePrompts: [],
          conceptRebuild: {},
          socraticQuestions: [],
          teachSession: {}
        };
      }

      setNote(data);

      // Calculate feature scores
      const scores = calculateFeatureScores(data);
      setFeatureScores(scores);
    } catch (error) {
      setError(error.toString());
      console.error('Error fetching note:', error);
    } finally {
      setLoading(false);
    }
  };

  // Fetch note data when component mounts or ID changes
  useEffect(() => {
    fetchNote();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [id]);

  const handleDeleteClick = () => {
    setOpenDialog(true);
  };

  const handleCloseDialog = () => {
    setOpenDialog(false);
  };

  const handleConfirmDelete = async () => {
    try {
      await removeNote(id);
      navigate('/dashboard');
    } catch (error) {
      console.error('Error deleting note:', error);
    }
  };

  // Format date
  const formatDate = (dateString) => {
    const options = { year: 'numeric', month: 'long', day: 'numeric', hour: '2-digit', minute: '2-digit' };
    return new Date(dateString).toLocaleDateString(undefined, options);
  };

  if (loading) {
    return (
      <Container maxWidth="lg" sx={{ py: 4, textAlign: 'center' }}>
        <CircularProgress size={48} />
        <Typography variant="body1" color="text.secondary" sx={{ mt: 2 }}>
          Loading note...
        </Typography>
      </Container>
    );
  }

  if (error) {
    return (
      <Container maxWidth="lg" sx={{ py: 4 }}>
        <Alert severity="error" sx={{ mb: 3 }}>
          {error}
        </Alert>
        <Button
          variant="contained"
          onClick={() => navigate('/dashboard')}
          sx={{ mt: 2 }}
        >
          Back to Dashboard
        </Button>
      </Container>
    );
  }

  if (!note) {
    return (
      <Container maxWidth="lg" sx={{ py: 4, textAlign: 'center' }}>
        <Typography variant="h5" component="h2" gutterBottom>
          Note not found
        </Typography>
        <Button
          variant="contained"
          component={RouterLink}
          to="/dashboard"
          sx={{ mt: 2 }}
        >
          Back to Dashboard
        </Button>
      </Container>
    );
  }

  return (
    <Container maxWidth="lg" sx={{ py: 4 }}>
      <Box sx={{ mb: 3, display: 'flex', alignItems: 'center' }}>
        <Button
          component={RouterLink}
          to="/dashboard"
          startIcon={<ArrowBackIcon />}
          sx={{ color: 'primary.main' }}
        >
          Back to Dashboard
        </Button>
      </Box>

      <Paper elevation={2} sx={{ p: 4, mb: 4 }}>
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start', mb: 3 }}>
          <Box>
            <Typography variant="h4" component="h1" fontWeight="bold" sx={{ mb: 2 }}>
              {note.title}
            </Typography>

            {/* Feature Score Indicators */}
            <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
              <ScoreIndicatorGroup
                scores={featureScores}
                size="medium"
                showLabels={true}
                spacing={2}
              />
            </Box>
          </Box>
          <Box>
            <IconButton
              component={RouterLink}
              to={`/notes/${note._id}/edit`}
              aria-label="edit"
              sx={{ mr: 1 }}
              color="primary"
            >
              <EditIcon />
            </IconButton>
            <IconButton
              onClick={handleDeleteClick}
              aria-label="delete"
              color="error"
            >
              <DeleteIcon />
            </IconButton>
          </Box>
        </Box>

        <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1, mb: 2 }}>
          {note.tags.map((tag) => (
            <Chip
              key={tag}
              label={`#${tag}`}
              size="small"
              sx={{ bgcolor: 'primary.50', color: 'primary.800' }}
            />
          ))}
        </Box>

        <Typography variant="body2" color="text.secondary" sx={{ mb: 3 }}>
          Last updated: {formatDate(note.updatedAt)}
        </Typography>

        <Divider sx={{ mb: 3 }} />

        <Box
          sx={{
            '& img': {
              maxWidth: '100%',
              height: 'auto',
            },
            '& a': {
              color: 'primary.main',
              textDecoration: 'underline',
            },
            '& blockquote': {
              borderLeft: '3px solid #ddd',
              margin: '0 0 1em 0',
              padding: '0 0 0 1em',
              color: 'text.secondary',
            },
          }}
          dangerouslySetInnerHTML={{ __html: note.content }}
        />
      </Paper>

      <AiFeatures note={note} refreshNote={fetchNote} />

      {/* Confirmation Dialog */}
      <Dialog
        open={openDialog}
        onClose={handleCloseDialog}
      >
        <DialogTitle>Delete Note</DialogTitle>
        <DialogContent>
          <DialogContentText>
            Are you sure you want to delete "{note.title}"? This action cannot be undone.
          </DialogContentText>
        </DialogContent>
        <DialogActions>
          <Button onClick={handleCloseDialog}>Cancel</Button>
          <Button onClick={handleConfirmDelete} color="error">
            Delete
          </Button>
        </DialogActions>
      </Dialog>
    </Container>
  );
};

export default NoteDetailPage;
