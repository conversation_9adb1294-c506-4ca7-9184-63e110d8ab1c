import { useState, useEffect, useContext } from 'react';
import { Link as RouterLink, useNavigate } from 'react-router-dom';
import { AuthContext } from '../context/AuthContext';
import { getDashboardStats } from '../api/noteApi';
import { getRoadmapStats } from '../api/roadmapApi';
import {
  Box,
  Button,
  Container,
  Grid,
  Paper,
  Typography,
  Card,
  CardContent,
  CardActions,
  CardHeader,
  Divider,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
  ListItemButton,
  CircularProgress,
  Alert,
  Chip,
  LinearProgress,
  IconButton,
  Tooltip,
  Avatar,
  useTheme,
  alpha,
} from '@mui/material';
import {
  Add as AddIcon,
  Note as NoteIcon,
  School as SchoolIcon,
  Assignment as AssignmentIcon,
  Timeline as TimelineIcon,
  CheckCircle as CheckCircleIcon,
  AccessTime as AccessTimeIcon,
  TrendingUp as TrendingUpIcon,
  Warning as WarningIcon,
  ArrowForward as ArrowForwardIcon,
  Dashboard as DashboardIcon,
  Lightbulb as LightbulbIcon,
  Insights as InsightsIcon,
  BarChart as BarChartIcon,
} from '@mui/icons-material';
import {
  BarChart, Bar, XAxis, YAxis, CartesianGrid, Tooltip as RechartsTooltip, ResponsiveContainer,
  PieChart, Pie, Cell, Legend, RadialBarChart, RadialBar, Area, AreaChart
} from 'recharts';

// Custom circular progress with label
const CircularProgressWithLabel = ({ value, size = 80, thickness = 6, color = 'primary', icon }) => {
  const theme = useTheme();

  // Get color based on value
  const getColor = (val) => {
    if (val >= 80) return theme.palette.success.main;
    if (val >= 50) return theme.palette.warning.main;
    return theme.palette.error.main;
  };

  const progressColor = color === 'primary' ? getColor(value) : theme.palette[color].main;

  return (
    <Box sx={{ position: 'relative', display: 'inline-flex' }}>
      <CircularProgress
        variant="determinate"
        value={100}
        size={size}
        thickness={thickness}
        sx={{ color: alpha(progressColor, 0.2) }}
      />
      <CircularProgress
        variant="determinate"
        value={value}
        size={size}
        thickness={thickness}
        sx={{
          color: progressColor,
          position: 'absolute',
          left: 0,
          '& circle': {
            strokeLinecap: 'round',
          }
        }}
      />
      <Box
        sx={{
          top: 0,
          left: 0,
          bottom: 0,
          right: 0,
          position: 'absolute',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          flexDirection: 'column',
        }}
      >
        {icon && (
          <Box sx={{ mb: 0.5, color: progressColor }}>
            {icon}
          </Box>
        )}
        <Typography
          variant="caption"
          component="div"
          sx={{
            fontWeight: 'bold',
            color: progressColor,
            fontSize: size > 100 ? '1.2rem' : '0.875rem'
          }}
        >
          {`${Math.round(value)}%`}
        </Typography>
      </Box>
    </Box>
  );
};

const EnhancedDashboardPage = () => {
  const { user } = useContext(AuthContext);
  const navigate = useNavigate();
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [noteStats, setNoteStats] = useState({
    totalNotes: 0,
    recentNotes: [],
    averageConfidence: 0,
    lowConfidenceNotes: [],
    notes: []
  });
  const [roadmapStats, setRoadmapStats] = useState({
    totalRoadmaps: 0,
    completedRoadmaps: 0,
    inProgressRoadmaps: 0,
    completionPercentage: 0,
    upcomingModules: [],
    roadmaps: []
  });

  useEffect(() => {
    const fetchDashboardData = async () => {
      try {
        setLoading(true);
        const [noteData, roadmapData] = await Promise.all([
          getDashboardStats(),
          getRoadmapStats()
        ]);

        setNoteStats(noteData);
        setRoadmapStats(roadmapData);
      } catch (error) {
        console.error('Error fetching dashboard data:', error);
        setError('Failed to load dashboard data. Please try again later.');
      } finally {
        setLoading(false);
      }
    };

    fetchDashboardData();
  }, []);

  // Prepare data for charts
  const confidenceDistribution = [
    {
      name: 'High (80-100%)', value: noteStats.notes.filter(note =>
        note.suggestions?.teachSession?.feedback?.confidenceScore >= 80).length
    },
    {
      name: 'Medium (50-79%)', value: noteStats.notes.filter(note =>
        note.suggestions?.teachSession?.feedback?.confidenceScore >= 50 &&
        note.suggestions?.teachSession?.feedback?.confidenceScore < 80).length
    },
    {
      name: 'Low (0-49%)', value: noteStats.notes.filter(note =>
        note.suggestions?.teachSession?.feedback?.confidenceScore < 50 &&
        note.suggestions?.teachSession?.feedback?.confidenceScore !== undefined).length
    },
    {
      name: 'Not Assessed', value: noteStats.notes.filter(note =>
        note.suggestions?.teachSession?.feedback?.confidenceScore === undefined).length
    }
  ];

  const COLORS = ['#4caf50', '#ff9800', '#f44336', '#9e9e9e'];

  const roadmapStatusData = [
    { name: 'Completed', value: roadmapStats.completedRoadmaps },
    { name: 'In Progress', value: roadmapStats.inProgressRoadmaps }
  ];

  const STATUS_COLORS = ['#4caf50', '#2196f3'];

  if (loading) {
    return (
      <Container maxWidth="lg" sx={{ py: 8 }}>
        <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '50vh' }}>
          <CircularProgress />
        </Box>
      </Container>
    );
  }

  if (error) {
    return (
      <Container maxWidth="lg" sx={{ py: 8 }}>
        <Alert severity="error" sx={{ mb: 4 }}>
          {error}
        </Alert>
        <Button variant="contained" onClick={() => window.location.reload()}>
          Retry
        </Button>
      </Container>
    );
  }

  const theme = useTheme();

  return (
    <Box sx={{
      backgroundColor: alpha(theme.palette.primary.light, 0.05),
      minHeight: '100vh',
      pt: 3,
      pb: 6
    }}>
      <Container maxWidth="lg">
        {/* Header */}
        <Paper
          elevation={0}
          sx={{
            mb: 4,
            p: 3,
            borderRadius: 2,
            background: `linear-gradient(135deg, ${theme.palette.primary.main} 0%, ${theme.palette.primary.dark} 100%)`,
            color: 'white',
            position: 'relative',
            overflow: 'hidden'
          }}
        >
          <Box sx={{ position: 'absolute', top: 0, right: 0, p: 2, opacity: 0.2 }}>
            <DashboardIcon sx={{ fontSize: 100 }} />
          </Box>
          <Box sx={{ position: 'relative', zIndex: 1 }}>
            <Typography variant="h4" component="h1" fontWeight="bold" gutterBottom>
              Learning Dashboard
            </Typography>
            <Typography variant="body1" sx={{ mb: 2, opacity: 0.9 }}>
              Welcome back, {user?.name || 'Student'}! Here's an overview of your learning progress.
            </Typography>

            <Box sx={{ mt: 3, display: 'flex', gap: 3, flexWrap: 'wrap' }}>
              <Box sx={{ display: 'flex', alignItems: 'center' }}>
                <Avatar sx={{ bgcolor: 'rgba(255,255,255,0.2)', mr: 1 }}>
                  <NoteIcon />
                </Avatar>
                <Box>
                  <Typography variant="h5" fontWeight="bold">{noteStats.totalNotes}</Typography>
                  <Typography variant="body2" sx={{ opacity: 0.8 }}>Total Notes</Typography>
                </Box>
              </Box>

              <Box sx={{ display: 'flex', alignItems: 'center' }}>
                <Avatar sx={{ bgcolor: 'rgba(255,255,255,0.2)', mr: 1 }}>
                  <TimelineIcon />
                </Avatar>
                <Box>
                  <Typography variant="h5" fontWeight="bold">{roadmapStats.totalRoadmaps}</Typography>
                  <Typography variant="body2" sx={{ opacity: 0.8 }}>Roadmaps</Typography>
                </Box>
              </Box>

              <Box sx={{ display: 'flex', alignItems: 'center' }}>
                <Avatar sx={{ bgcolor: 'rgba(255,255,255,0.2)', mr: 1 }}>
                  <TrendingUpIcon />
                </Avatar>
                <Box>
                  <Typography variant="h5" fontWeight="bold">{noteStats.averageConfidence}%</Typography>
                  <Typography variant="body2" sx={{ opacity: 0.8 }}>Avg. Confidence</Typography>
                </Box>
              </Box>
            </Box>
          </Box>
        </Paper>

        {/* Main Dashboard Grid */}
        <Grid container spacing={2.5}>
          {/* Left Column */}
          <Grid item xs={12} md={8} sx={{display:"grid",
                gridTemplateColumns:"1fr 1fr",gap:"20px"}}>
            {/* Knowledge Progress Section */}
            <Paper
              elevation={0}
              sx={{
                p: 2.5,
                mb: 3,
                borderRadius: 2,
                boxShadow: theme.shadows[1],
              }}
            >
              <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
                <Box sx={{ display: 'flex', alignItems: 'center' }}>
                  <Avatar sx={{ bgcolor: alpha(theme.palette.primary.main, 0.1), color: theme.palette.primary.main, mr: 2 }}>
                    <InsightsIcon />
                  </Avatar>
                  <Typography variant="h6">Knowledge Progress</Typography>
                </Box>
                <Chip
                  label={`${noteStats.averageConfidence}% Average`}
                  color={noteStats.averageConfidence >= 80 ? "success" : noteStats.averageConfidence >= 50 ? "warning" : "error"}
                  size="small"
                />
              </Box>

              <Grid container spacing={2} sx={{ mb: 2 }}>
                <Grid item xs={12} sm={6}>
                  <Box sx={{ display: 'flex', justifyContent: 'center', mb: 2 }}>
                    <CircularProgressWithLabel
                      value={noteStats.averageConfidence}
                      size={100}
                      thickness={8}
                      icon={<TrendingUpIcon fontSize="small" />}
                    />
                  </Box>
                  <Typography variant="body2" color="text.secondary" align="center">
                    Average confidence score across all notes
                  </Typography>
                </Grid>

                <Grid item xs={12} sm={6}>
                  <Box sx={{ height: 130 }}>
                    <ResponsiveContainer width="100%" height="100%">
                      <BarChart
                        data={[
                          { name: 'High', value: confidenceDistribution[0].value, fill: theme.palette.success.main },
                          { name: 'Medium', value: confidenceDistribution[1].value, fill: theme.palette.warning.main },
                          { name: 'Low', value: confidenceDistribution[2].value, fill: theme.palette.error.main },
                        ]}
                        margin={{ top: 10, right: 10, left: 10, bottom: 10 }}
                      >
                        <CartesianGrid strokeDasharray="3 3" vertical={false} opacity={0.2} />
                        <XAxis dataKey="name" axisLine={false} tickLine={false} />
                        <YAxis hide />
                        <RechartsTooltip formatter={(value) => [`${value} notes`, 'Count']} />
                        <Bar dataKey="value" radius={[4, 4, 0, 0]} />
                      </BarChart>
                    </ResponsiveContainer>
                  </Box>
                  <Typography variant="body2" color="text.secondary" align="center">
                    Distribution of knowledge levels
                  </Typography>
                </Grid>
              </Grid>

              <Box sx={{ display: 'flex', justifyContent: 'flex-end', mt: 2 }}>
                <Button
                  variant="outlined"
                  size="small"
                  component={RouterLink}
                  to="/notes"
                  endIcon={<ArrowForwardIcon />}
                >
                  View All Notes
                </Button>
              </Box>
            </Paper>

            {/* Recent Notes Section */}
            <Paper
              elevation={0}
              sx={{
                p: 2.5,
                mb: 3,
                borderRadius: 2,
                boxShadow: theme.shadows[1]
              }}
            >
              <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
                <Box sx={{ display: 'flex', alignItems: 'center' }}>
                  <Avatar sx={{ bgcolor: alpha(theme.palette.info.main, 0.1), color: theme.palette.info.main, mr: 2 }}>
                    <NoteIcon />
                  </Avatar>
                  <Typography variant="h6">Recent Notes</Typography>
                </Box>
                <Chip
                  label={`${noteStats.totalNotes} Total`}
                  color="primary"
                  size="small"
                />
              </Box>

              <Box sx={{ maxHeight: 300, overflow: 'auto', mb: 2 }}>
                {noteStats.recentNotes.length > 0 ? (
                  <List sx={{ p: 0 }}>
                    {noteStats.recentNotes.map((note) => (
                      <ListItem
                        key={note._id}
                        disablePadding
                        sx={{ mb: 1 }}
                      >
                        <Card variant="outlined" sx={{ width: '100%', borderRadius: 2 }}>
                          <CardContent sx={{ py: 2, '&:last-child': { pb: 2 } }}>
                            <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                              <Box sx={{ display: 'flex', alignItems: 'center' }}>
                                <Avatar
                                  sx={{
                                    width: 40,
                                    height: 40,
                                    mr: 2,
                                    bgcolor: alpha(theme.palette.primary.main, 0.1),
                                    color: theme.palette.primary.main
                                  }}
                                >
                                  <NoteIcon fontSize="small" />
                                </Avatar>
                                <Box>
                                  <Typography variant="subtitle1" noWrap sx={{ maxWidth: 200 }}>
                                    {note.title}
                                  </Typography>
                                  <Typography variant="caption" color="text.secondary">
                                    Last updated: {new Date(note.updatedAt).toLocaleDateString()}
                                  </Typography>
                                </Box>
                              </Box>

                              <Box sx={{ display: 'flex', alignItems: 'center' }}>
                                {note.suggestions?.teachSession?.feedback?.confidenceScore && (
                                  <Chip
                                    label={`${note.suggestions.teachSession.feedback.confidenceScore}%`}
                                    color={
                                      note.suggestions.teachSession.feedback.confidenceScore >= 80
                                        ? 'success'
                                        : note.suggestions.teachSession.feedback.confidenceScore >= 50
                                          ? 'warning'
                                          : 'error'
                                    }
                                    size="small"
                                    sx={{ mr: 1 }}
                                  />
                                )}
                                <IconButton size="small" onClick={() => navigate(`/notes/${note._id}`)}>
                                  <ArrowForwardIcon fontSize="small" />
                                </IconButton>
                              </Box>
                            </Box>
                          </CardContent>
                        </Card>
                      </ListItem>
                    ))}
                  </List>
                ) : (
                  <Typography variant="body2" color="text.secondary" sx={{ textAlign: 'center', py: 4 }}>
                    No notes created yet.
                  </Typography>
                )}
              </Box>

              <Box sx={{ display: 'flex', justifyContent: 'flex-end' }}>
                <Button
                  variant="contained"
                  size="small"
                  component={RouterLink}
                  to="/notes/new"
                  startIcon={<AddIcon />}
                >
                  Create New Note
                </Button>
              </Box>
            </Paper>
          </Grid>

          {/* Right Column */}
          <Grid item xs={12} md={4}>
            {/* Roadmap Progress */}
            <Paper
              elevation={0}
              sx={{
                p: 2.5,
                mb: 3,
                borderRadius: 2,
                boxShadow: theme.shadows[1]
              }}
            >
              <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
                <Box sx={{ display: 'flex', alignItems: 'center' }}>
                  <Avatar sx={{ bgcolor: alpha(theme.palette.success.main, 0.1), color: theme.palette.success.main, mr: 2 }}>
                    <TimelineIcon />
                  </Avatar>
                  <Typography variant="h6">Roadmap Progress</Typography>
                </Box>
                <Chip
                  label={`${roadmapStats.totalRoadmaps} Total`}
                  color="secondary"
                  size="small"
                />
              </Box>

              <Box sx={{ display: 'flex', justifyContent: 'center', mb: 2 }}>
                <CircularProgressWithLabel
                  value={roadmapStats.completionPercentage}
                  size={100}
                  thickness={8}
                  color="success"
                  icon={<CheckCircleIcon fontSize="small" />}
                />
              </Box>
              <Typography variant="body2" color="text.secondary" align="center" sx={{ mb: 3 }}>
                Overall completion rate
              </Typography>

              <Box sx={{ height: 130, mb: 2 }}>
                <ResponsiveContainer width="100%" height="100%">
                  <PieChart>
                    <Pie
                      data={roadmapStatusData}
                      cx="50%"
                      cy="50%"
                      innerRadius={40}
                      outerRadius={60}
                      fill="#8884d8"
                      dataKey="value"
                      paddingAngle={5}
                    >
                      {roadmapStatusData.map((entry, index) => (
                        <Cell key={`cell-${index}`} fill={STATUS_COLORS[index % STATUS_COLORS.length]} cornerRadius={4} />
                      ))}
                    </Pie>
                    <Legend verticalAlign="bottom" height={20} />
                    <RechartsTooltip formatter={(value) => [`${value} roadmaps`, 'Count']} />
                  </PieChart>
                </ResponsiveContainer>
              </Box>

              <Box sx={{ display: 'flex', justifyContent: 'flex-end' }}>
                <Button
                  variant="outlined"
                  size="small"
                  component={RouterLink}
                  to="/roadmaps"
                  endIcon={<ArrowForwardIcon />}
                >
                  View All Roadmaps
                </Button>
              </Box>
            </Paper>

            {/* Upcoming Learning Modules */}
            <Paper
              elevation={0}
              sx={{
                p: 2.5,
                mb: 3,
                borderRadius: 2,
                boxShadow: theme.shadows[1]
              }}
            >
              <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
                <Box sx={{ display: 'flex', alignItems: 'center' }}>
                  <Avatar sx={{ bgcolor: alpha(theme.palette.warning.main, 0.1), color: theme.palette.warning.main, mr: 2 }}>
                    <AccessTimeIcon />
                  </Avatar>
                  <Typography variant="h6">Upcoming Modules</Typography>
                </Box>
              </Box>

              <Box sx={{ maxHeight: 300, overflow: 'auto', mb: 2 }}>
                {roadmapStats.upcomingModules.length > 0 ? (
                  <List sx={{ p: 0 }}>
                    {roadmapStats.upcomingModules.map((module) => (
                      <ListItem
                        key={module.moduleId}
                        disablePadding
                        sx={{ mb: 1 }}
                      >
                        <Card variant="outlined" sx={{ width: '100%', borderRadius: 2 }}>
                          <CardContent sx={{ py: 2, '&:last-child': { pb: 2 } }}>
                            <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                              <Box sx={{ display: 'flex', alignItems: 'center' }}>
                                <Avatar
                                  sx={{
                                    width: 40,
                                    height: 40,
                                    mr: 2,
                                    bgcolor: alpha(theme.palette.secondary.main, 0.1),
                                    color: theme.palette.secondary.main
                                  }}
                                >
                                  <SchoolIcon fontSize="small" />
                                </Avatar>
                                <Box>
                                  <Typography variant="subtitle1" noWrap sx={{ maxWidth: 200 }}>
                                    {module.moduleTitle}
                                  </Typography>
                                  <Typography variant="caption" color="text.secondary">
                                    From: {module.roadmapTitle}
                                  </Typography>
                                </Box>
                              </Box>

                              <Box sx={{ display: 'flex', alignItems: 'center' }}>
                                <Chip
                                  icon={<AccessTimeIcon fontSize="small" />}
                                  label={module.timeframe}
                                  color="primary"
                                  size="small"
                                  sx={{ mr: 1 }}
                                />
                                <IconButton size="small" onClick={() => navigate(`/roadmaps/${module.roadmapId}`)}>
                                  <ArrowForwardIcon fontSize="small" />
                                </IconButton>
                              </Box>
                            </Box>
                          </CardContent>
                        </Card>
                      </ListItem>
                    ))}
                  </List>
                ) : (
                  <Typography variant="body2" color="text.secondary" sx={{ textAlign: 'center', py: 4 }}>
                    No upcoming modules.
                  </Typography>
                )}
              </Box>
            </Paper>

            {/* Notes Needing Improvement */}
            <Paper
              elevation={0}
              sx={{
                p: 2.5,
                borderRadius: 2,
                boxShadow: theme.shadows[1]
              }}
            >
              <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
                <Box sx={{ display: 'flex', alignItems: 'center' }}>
                  <Avatar sx={{ bgcolor: alpha(theme.palette.error.main, 0.1), color: theme.palette.error.main, mr: 2 }}>
                    <WarningIcon />
                  </Avatar>
                  <Typography variant="h6">Notes Needing Improvement</Typography>
                </Box>
              </Box>

              {noteStats.lowConfidenceNotes.length > 0 ? (
                <Grid container spacing={2}>
                  {noteStats.lowConfidenceNotes.map((note) => (
                    <Grid item xs={12} key={note._id}>
                      <Card variant="outlined" sx={{ borderRadius: 2, borderColor: alpha(theme.palette.warning.main, 0.5) }}>
                        <CardContent sx={{ py: 2, '&:last-child': { pb: 2 } }}>
                          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                            <Box sx={{ display: 'flex', alignItems: 'center' }}>
                              <Avatar
                                sx={{
                                  width: 40,
                                  height: 40,
                                  mr: 2,
                                  bgcolor: alpha(theme.palette.warning.main, 0.1),
                                  color: theme.palette.warning.main
                                }}
                              >
                                <WarningIcon fontSize="small" />
                              </Avatar>
                              <Box>
                                <Typography variant="subtitle1">
                                  {note.title}
                                </Typography>
                                <Box sx={{ mt: 1, width: '100%' }}>
                                  <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                                    <Typography variant="caption" color="text.secondary" sx={{ mr: 1 }}>
                                      Confidence Score:
                                    </Typography>
                                    <Typography variant="caption" fontWeight="bold" color={
                                      (note.suggestions?.teachSession?.feedback?.confidenceScore || 0) >= 50
                                        ? theme.palette.warning.main
                                        : theme.palette.error.main
                                    }>
                                      {note.suggestions?.teachSession?.feedback?.confidenceScore || 0}%
                                    </Typography>
                                  </Box>
                                  <LinearProgress
                                    variant="determinate"
                                    value={note.suggestions?.teachSession?.feedback?.confidenceScore || 0}
                                    color={
                                      (note.suggestions?.teachSession?.feedback?.confidenceScore || 0) >= 50
                                        ? 'warning'
                                        : 'error'
                                    }
                                    sx={{ height: 6, borderRadius: 3 }}
                                  />
                                </Box>
                              </Box>
                            </Box>

                            <Box>
                              <Button
                                variant="outlined"
                                size="small"
                                onClick={() => navigate(`/notes/${note._id}`)}
                                sx={{ mr: 1 }}
                              >
                                Review
                              </Button>
                              <Button
                                variant="contained"
                                size="small"
                                color="secondary"
                                onClick={() => navigate(`/roadmaps/${note._id}`)}
                                startIcon={<SchoolIcon />}
                              >
                                Create Roadmap
                              </Button>
                            </Box>
                          </Box>
                        </CardContent>
                      </Card>
                    </Grid>
                  ))}
                </Grid>
              ) : (
                <Box sx={{ textAlign: 'center', py: 4 }}>
                  <CheckCircleIcon color="success" sx={{ fontSize: 48, mb: 2, opacity: 0.7 }} />
                  <Typography variant="body1" color="text.secondary">
                    No notes need improvement. Great job!
                  </Typography>
                </Box>
              )}
            </Paper>
          </Grid>
        </Grid>
      </Container>
    </Box>
  );

};

export default EnhancedDashboardPage;
