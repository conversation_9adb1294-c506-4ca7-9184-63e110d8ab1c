import { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { getRoadmaps } from '../api/roadmapApi';
import {
  Box,
  Container,
  Typography,
  Paper,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
  ListItemSecondaryAction,
  IconButton,
  Button,
  Chip,
  Divider,
  Alert,
  CircularProgress,
} from '@mui/material';
import {
  School as SchoolIcon,
  CheckCircle as CheckCircleIcon,
  ArrowForward as ArrowForwardIcon,
  Timer as TimerIcon,
} from '@mui/icons-material';

const RoadmapsListPage = () => {
  const navigate = useNavigate();
  const [roadmaps, setRoadmaps] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  useEffect(() => {
    const fetchRoadmaps = async () => {
      try {
        const data = await getRoadmaps();
        setRoadmaps(data);
      } catch (error) {
        setError('Failed to load roadmaps');
        console.error(error);
      } finally {
        setLoading(false);
      }
    };

    fetchRoadmaps();
  }, []);

  const handleViewRoadmap = (id) => {
    navigate(`/roadmaps/${id}`);
  };

  if (loading) {
    return (
      <Container maxWidth="md" sx={{ py: 4 }}>
        <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '50vh' }}>
          <CircularProgress />
        </Box>
      </Container>
    );
  }

  if (error) {
    return (
      <Container maxWidth="md" sx={{ py: 4 }}>
        <Alert severity="error" sx={{ mb: 2 }}>
          {error}
        </Alert>
        <Button onClick={() => navigate('/')}>Back to Dashboard</Button>
      </Container>
    );
  }

  return (
    <Container maxWidth="md" sx={{ py: 4 }}>
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
        <Typography variant="h4" component="h1">
          Learning Roadmaps
        </Typography>
        <Button onClick={() => navigate('/')}>Back to Dashboard</Button>
      </Box>

      {roadmaps.length === 0 ? (
        <Paper sx={{ p: 3, textAlign: 'center' }}>
          <SchoolIcon sx={{ fontSize: 60, color: 'primary.main', mb: 2 }} />
          <Typography variant="h6" gutterBottom>
            No Learning Roadmaps Yet
          </Typography>
          <Typography variant="body1" color="text.secondary" paragraph>
            You haven't created any learning roadmaps yet. Roadmaps are created when you need to improve your understanding of a concept.
          </Typography>
          <Button variant="contained" onClick={() => navigate('/')}>
            Go to Notes
          </Button>
        </Paper>
      ) : (
        <Paper sx={{ p: 0 }}>
          <List sx={{ width: '100%', bgcolor: 'background.paper' }}>
            {roadmaps.map((roadmap, index) => (
              <Box key={roadmap._id}>
                {index > 0 && <Divider component="li" />}
                <ListItem
                  alignItems="flex-start"
                  button
                  onClick={() => handleViewRoadmap(roadmap._id)}
                  sx={{ 
                    p: 2,
                    '&:hover': { bgcolor: 'action.hover' },
                    bgcolor: roadmap.completed ? 'success.50' : 'background.paper'
                  }}
                >
                  <ListItemIcon>
                    <SchoolIcon color="primary" />
                  </ListItemIcon>
                  <ListItemText
                    primary={
                      <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                        <Typography variant="h6" component="span">
                          {roadmap.title}
                        </Typography>
                        {roadmap.completed && (
                          <Chip
                            size="small"
                            icon={<CheckCircleIcon />}
                            label="Completed"
                            color="success"
                          />
                        )}
                      </Box>
                    }
                    secondary={
                      <Box sx={{ mt: 1 }}>
                        <Typography variant="body2" color="text.primary" paragraph>
                          {roadmap.overview?.substring(0, 150)}
                          {roadmap.overview?.length > 150 ? '...' : ''}
                        </Typography>
                        <Box sx={{ display: 'flex', alignItems: 'center', mt: 1 }}>
                          <TimerIcon fontSize="small" sx={{ mr: 0.5 }} color="action" />
                          <Typography variant="body2" color="text.secondary">
                            Created {new Date(roadmap.createdAt).toLocaleDateString()}
                          </Typography>
                          <Box sx={{ ml: 2 }}>
                            <Typography variant="body2" color="text.secondary">
                              {roadmap.modules?.length || 0} modules
                            </Typography>
                          </Box>
                        </Box>
                      </Box>
                    }
                  />
                  <ListItemSecondaryAction>
                    <IconButton edge="end" onClick={() => handleViewRoadmap(roadmap._id)}>
                      <ArrowForwardIcon />
                    </IconButton>
                  </ListItemSecondaryAction>
                </ListItem>
              </Box>
            ))}
          </List>
        </Paper>
      )}
    </Container>
  );
};

export default RoadmapsListPage;
