/**
 * Calculate scores for each AI feature from a note
 * @param {Object} note - The note object
 * @returns {Object} Object containing scores for each feature
 */
export const calculateFeatureScores = (note) => {
  if (!note || !note.aiSuggestions) {
    console.log('Note or aiSuggestions is missing:', note);

    return {
      reflective: null,
      concept: null,
      socratic: null,
      teach: null
    };
  }

  const { aiSuggestions } = note;

  // Calculate reflective prompts score
  const reflectiveScore = calculateReflectiveScore(aiSuggestions.reflectivePrompts);

  // Calculate concept rebuilder score
  const conceptScore = calculateConceptScore(aiSuggestions.conceptRebuild);

  // Calculate socratic questions score
  const socraticScore = calculateSocraticScore(aiSuggestions.socraticQuestions);

  // Get teaching AI score (already calculated by the AI)
  const teachScore = aiSuggestions.teachSession?.feedback?.confidenceScore || null;

  return {
    reflective: reflectiveScore,
    concept: conceptScore,
    socratic: socraticScore,
    teach: teachScore
  };
};

/**
 * Calculate score for reflective prompts based on completion
 * @param {Array} reflectivePrompts - Array of reflective prompts
 * @returns {Number|null} Score or null if not started
 */
const calculateReflectiveScore = (reflectivePrompts) => {
  if (!reflectivePrompts || reflectivePrompts.length === 0) {
    return null;
  }

  // Count prompts with responses
  const totalPrompts = reflectivePrompts.length;
  const answeredPrompts = reflectivePrompts.filter(p =>
    p.response && p.response.trim().length > 0
  ).length;

  // If no prompts have been answered, return null
  if (answeredPrompts === 0) {
    return null;
  }

  // Calculate completion percentage
  const completionPercentage = (answeredPrompts / totalPrompts) * 100;

  // For reflective prompts, we'll use a simple completion score
  // We could make this more sophisticated in the future
  return Math.round(completionPercentage);
};

/**
 * Calculate score for concept rebuilder based on completion
 * @param {Object} conceptRebuild - Concept rebuilder object
 * @returns {Number|null} Score or null if not started
 */
const calculateConceptScore = (conceptRebuild) => {
  if (!conceptRebuild || !conceptRebuild.simplifiedExplanation) {
    return null;
  }

  // If there's no user response, return a base score
  if (!conceptRebuild.userResponse || conceptRebuild.userResponse.trim().length === 0) {
    return 50; // Base score for just having the explanation
  }

  // For concept rebuilder, we'll use a simple length-based score
  // We could make this more sophisticated in the future
  const responseLength = conceptRebuild.userResponse.trim().length;
  const explanationLength = conceptRebuild.simplifiedExplanation.trim().length;

  // Calculate a score based on response length relative to explanation
  // (with a cap at 100)
  const lengthScore = Math.min(100, Math.round((responseLength / explanationLength) * 100));

  // Return a weighted score (70% completion, 30% length)
  return Math.round(0.7 * 100 + 0.3 * lengthScore);
};

/**
 * Calculate score for socratic questions based on completion
 * @param {Array} socraticQuestions - Array of socratic questions
 * @returns {Number|null} Score or null if not started
 */
const calculateSocraticScore = (socraticQuestions) => {
  if (!socraticQuestions || socraticQuestions.length === 0) {
    return null;
  }

  // Count questions with responses
  const totalQuestions = socraticQuestions.length;
  const answeredQuestions = socraticQuestions.filter(q =>
    q.response && q.response.trim().length > 0
  ).length;

  // If no questions have been answered, return null
  if (answeredQuestions === 0) {
    return null;
  }

  // Calculate completion percentage
  const completionPercentage = (answeredQuestions / totalQuestions) * 100;

  // For socratic questions, we'll use a simple completion score
  // We could make this more sophisticated in the future
  return Math.round(completionPercentage);
};
