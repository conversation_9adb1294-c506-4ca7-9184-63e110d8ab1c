import { createContext, useState, useEffect, useContext, useCallback } from 'react';
import { getNotes, createNote, updateNote, deleteNote, updateAiSuggestions } from '../api/noteApi';
import { AuthContext } from './AuthContext';

export const NoteContext = createContext();

export const NoteProvider = ({ children }) => {
  const [notes, setNotes] = useState([]);
  const [currentNote, setCurrentNote] = useState(null);
  const [loading, setLoading] = useState(false);
  const [fetchedInitialData, setFetchedInitialData] = useState(false);
  const [operationLoading, setOperationLoading] = useState({
    create: false,
    update: false,
    delete: false,
    aiUpdate: false,
  });
  const [error, setError] = useState(null);
  const { isAuthenticated } = useContext(AuthContext);

  // Fetch all notes - memoized to prevent infinite loops
  const fetchNotes = useCallback(async (force = false) => {
    // Skip fetching if we've already fetched and force is false
    if (fetchedInitialData && !force) {
      return;
    }

    // Skip if already loading
    if (loading) {
      return;
    }

    setLoading(true);
    setError(null);
    try {
      const data = await getNotes();
      setNotes(data);
      setFetchedInitialData(true);
    } catch (error) {
      setError(error.toString());
      console.error('Error fetching notes:', error);
    } finally {
      setLoading(false);
    }
  }, [loading, fetchedInitialData]);

  // Fetch all notes when authenticated
  useEffect(() => {
    if (isAuthenticated) {
      fetchNotes();
    } else {
      setNotes([]);
      setCurrentNote(null);
      setFetchedInitialData(false);
    }
  }, [isAuthenticated, fetchNotes]);

  // Create a new note
  const addNote = async (noteData) => {
    setOperationLoading(prev => ({ ...prev, create: true }));
    setError(null);
    try {
      const newNote = await createNote(noteData);
      setNotes((prevNotes) => [newNote, ...prevNotes]);
      return newNote;
    } catch (error) {
      setError(error.toString());
      console.error('Error creating note:', error);
      throw error;
    } finally {
      setOperationLoading(prev => ({ ...prev, create: false }));
    }
  };

  // Update a note
  const editNote = async (noteId, noteData) => {
    setOperationLoading(prev => ({ ...prev, update: true }));
    setError(null);
    try {
      // Optimistic update
      const optimisticNote = { ...currentNote, ...noteData, updatedAt: new Date().toISOString() };
      if (currentNote && currentNote._id === noteId) {
        setCurrentNote(optimisticNote);
      }

      // Optimistically update the notes list
      setNotes((prevNotes) =>
        prevNotes.map((note) => (note._id === noteId ? { ...note, ...noteData } : note))
      );

      // Actual API call
      const updatedNote = await updateNote(noteId, noteData);

      // Update with server response
      setNotes((prevNotes) =>
        prevNotes.map((note) => (note._id === noteId ? updatedNote : note))
      );
      if (currentNote && currentNote._id === noteId) {
        setCurrentNote(updatedNote);
      }
      return updatedNote;
    } catch (error) {
      setError(error.toString());
      console.error('Error updating note:', error);
      throw error;
    } finally {
      setOperationLoading(prev => ({ ...prev, update: false }));
    }
  };

  // Delete a note
  const removeNote = async (noteId) => {
    setOperationLoading(prev => ({ ...prev, delete: true }));
    setError(null);
    try {
      // Optimistic delete
      setNotes((prevNotes) => prevNotes.filter((note) => note._id !== noteId));
      if (currentNote && currentNote._id === noteId) {
        setCurrentNote(null);
      }

      // Actual API call
      await deleteNote(noteId);
    } catch (error) {
      // Revert optimistic delete on error
      fetchNotes(); // Refresh notes from server
      setError(error.toString());
      console.error('Error deleting note:', error);
      throw error;
    } finally {
      setOperationLoading(prev => ({ ...prev, delete: false }));
    }
  };

  // Update AI suggestions for a note
  const updateNoteSuggestions = async (noteId, aiSuggestions) => {
    setOperationLoading(prev => ({ ...prev, aiUpdate: true }));
    setError(null);
    try {
      const updatedNote = await updateAiSuggestions(noteId, aiSuggestions);
      setNotes((prevNotes) =>
        prevNotes.map((note) => (note._id === noteId ? updatedNote : note))
      );
      if (currentNote && currentNote._id === noteId) {
        setCurrentNote(updatedNote);
      }
      return updatedNote;
    } catch (error) {
      setError(error.toString());
      console.error('Error updating AI suggestions:', error);
      throw error;
    } finally {
      setOperationLoading(prev => ({ ...prev, aiUpdate: false }));
    }
  };

  return (
    <NoteContext.Provider
      value={{
        notes,
        currentNote,
        setCurrentNote,
        loading,
        operationLoading,
        error,
        fetchNotes,
        addNote,
        editNote,
        removeNote,
        updateNoteSuggestions,
      }}
    >
      {children}
    </NoteContext.Provider>
  );
};
