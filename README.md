# AI-Driven Personalized Note-Taking App

An AI-powered note-taking application built with the MERN stack (MongoDB, Express, React, Node.js) that enhances learning through AI-driven features like reflective prompting, concept rebuilding, and Socratic questioning.

## Features

- **User Authentication**: Secure signup and login
- **Note Creation & Editing**: Create and edit notes with rich text formatting
- **Reflective Prompting**: AI generates reflective questions to deepen understanding
- **Concept Rebuilder**: Get simplified explanations of complex concepts
- **Socratic Mode**: Engage with AI-generated Socratic questions
- **Tagging & Concept Linking**: Organize notes with AI-suggested tags
- **Smart Recall Nudges**: Receive reminders to review notes at optimal intervals

## Tech Stack

### Backend
- Node.js with Express
- MongoDB with Mongoose
- JWT Authentication
- OpenAI API integration
- Winston for logging

### Frontend
- React with Vite
- React Router for navigation
- Material UI for styling and components
- TipTap for rich text editing

## Project Structure

```
/
├── backend/               # Backend server code
│   ├── config/            # Configuration files
│   ├── controllers/       # Route controllers
│   ├── logs/              # Log files
│   ├── middleware/        # Custom middleware
│   ├── models/            # Mongoose models
│   ├── routes/            # API routes
│   └── server.js          # Server entry point
│
└── frontend/              # Frontend React application
    ├── public/            # Static files
    └── src/               # Source files
        ├── api/           # API service functions
        ├── components/    # Reusable components
        ├── context/       # React context providers
        ├── hooks/         # Custom React hooks
        ├── pages/         # Page components
        └── utils/         # Utility functions
```

## Getting Started

### Prerequisites
- Node.js (v14 or higher)
- MongoDB
- OpenAI API key

### Installation

1. Clone the repository
   ```
   git clone <repository-url>
   cd ai-notes-app
   ```

2. Install backend dependencies
   ```
   cd backend
   npm install
   ```

3. Set up environment variables
   Create a `.env` file in the backend directory with the following variables:
   ```
   PORT=5000
   MONGO_URI=your_mongodb_connection_string
   JWT_SECRET=your_jwt_secret
   OPENAI_API_KEY=your_openai_api_key
   ```

4. Install frontend dependencies
   ```
   cd ../frontend
   npm install
   ```

### Running the Application

1. Start the backend server
   ```
   cd backend
   npm run dev
   ```

2. Start the frontend development server
   ```
   cd frontend
   npm run dev
   ```

3. Open your browser and navigate to `http://localhost:5173`

## Logging

The application uses Winston for logging. Logs are stored in the `backend/logs` directory:

- `combined.log`: Contains all logs (info, warn, error, debug)
- `error.log`: Contains only error logs

In development mode, logs are also output to the console.

## License

This project is licensed under the MIT License - see the LICENSE file for details.
