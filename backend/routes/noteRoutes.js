const express = require('express');
const router = express.Router();
const {
  getNotes,
  getNoteById,
  createNote,
  updateNote,
  deleteNote,
  updateAiSuggestions,
} = require('../controllers/noteController');
const { protect } = require('../middleware/authMiddleware');

// Protect all routes
router.use(protect);

// Get all notes and create a new note
router.route('/')
  .get(getNotes)
  .post(createNote);

// Get, update, and delete a specific note
router.route('/:id')
  .get(getNoteById)
  .put(updateNote)
  .delete(deleteNote);

// Update AI suggestions for a note
router.put('/:id/ai-suggestions', updateAiSuggestions);

module.exports = router;
