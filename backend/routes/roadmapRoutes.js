const express = require('express');
const router = express.Router();
const {
  createRoadmap,
  getRoadmaps,
  getRoadmapById,
  updateRoadmap,
  updateModuleStatus,
  deleteRoadmap,
} = require('../controllers/roadmapController');
const { protect } = require('../middleware/authMiddleware');

// Protect all routes
router.use(protect);

// Roadmap routes
router.route('/')
  .post(createRoadmap)
  .get(getRoadmaps);

router.route('/:id')
  .get(getRoadmapById)
  .put(updateRoadmap)
  .delete(deleteRoadmap);

router.route('/:id/modules/:moduleId')
  .put(updateModuleStatus);

module.exports = router;
