const express = require('express');
const router = express.Router();
const {
  generateReflectivePrompts,
  generateConceptRebuild,
  generateSocraticQuestions,
  extractTags,
  startTeachSession,
  submitExplanation,
  submitQuestionResponse,
  generateFeedback,
  generateRoadmap,
} = require('../controllers/aiController');
const { protect } = require('../middleware/authMiddleware');

// Protect all routes
router.use(protect);

// AI feature routes
router.post('/reflective-prompts', generateReflectivePrompts);
router.post('/concept-rebuild', generateConceptRebuild);
router.post('/socratic-questions', generateSocraticQuestions);
router.post('/extract-tags', extractTags);

// Teach AI routes
router.post('/teach-ai/start', startTeachSession);
router.post('/teach-ai/explain', submitExplanation);
router.post('/teach-ai/next-question', submitQuestionResponse);
router.post('/teach-ai/feedback', generateFeedback);
router.post('/generate-roadmap', generateRoadmap);

module.exports = router;
