const Roadmap = require('../models/roadmapModel');
const Note = require('../models/noteModel');
const logger = require('../config/logger');

// @desc    Create a new roadmap
// @route   POST /api/roadmaps
// @access  Private
const createRoadmap = async (req, res) => {
  try {
    const { noteId, roadmapData } = req.body;

    if (!roadmapData || !noteId) {
      return res.status(400).json({ message: 'Roadmap data and note ID are required' });
    }

    // Check if the note exists and belongs to the user
    const note = await Note.findOne({ _id: noteId, user: req.user.id });
    if (!note) {
      return res.status(404).json({ message: 'Note not found' });
    }

    // Create the roadmap
    const roadmap = await Roadmap.create({
      user: req.user.id,
      relatedNote: noteId,
      title: roadmapData.title,
      overview: roadmapData.overview,
      knowledgeGaps: roadmapData.knowledgeGaps,
      modules: roadmapData.modules,
      assessment: roadmapData.assessment,
      rawContent: roadmapData.rawContent,
      parseError: roadmapData.parseError || false,
    });

    res.status(201).json(roadmap);
  } catch (error) {
    logger.error('Error creating roadmap:', error);
    res.status(500).json({ message: 'Error creating roadmap' });
  }
};

// @desc    Get all roadmaps for a user
// @route   GET /api/roadmaps
// @access  Private
const getRoadmaps = async (req, res) => {
  try {
    const roadmaps = await Roadmap.find({ user: req.user.id }).sort({ createdAt: -1 });
    res.json(roadmaps);
  } catch (error) {
    logger.error('Error getting roadmaps:', error);
    res.status(500).json({ message: 'Error getting roadmaps' });
  }
};

// @desc    Get a roadmap by ID
// @route   GET /api/roadmaps/:id
// @access  Private
const getRoadmapById = async (req, res) => {
  try {
    const roadmap = await Roadmap.findOne({ _id: req.params.id, user: req.user.id });

    if (!roadmap) {
      return res.status(404).json({ message: 'Roadmap not found' });
    }

    res.json(roadmap);
  } catch (error) {
    logger.error('Error getting roadmap:', error);
    res.status(500).json({ message: 'Error getting roadmap' });
  }
};

// @desc    Update a roadmap
// @route   PUT /api/roadmaps/:id
// @access  Private
const updateRoadmap = async (req, res) => {
  try {
    const { title, overview, knowledgeGaps, modules, assessment, completed } = req.body;

    const roadmap = await Roadmap.findOne({ _id: req.params.id, user: req.user.id });

    if (!roadmap) {
      return res.status(404).json({ message: 'Roadmap not found' });
    }

    // Update the roadmap fields
    if (title) roadmap.title = title;
    if (overview) roadmap.overview = overview;
    if (knowledgeGaps) roadmap.knowledgeGaps = knowledgeGaps;
    if (modules) roadmap.modules = modules;
    if (assessment) roadmap.assessment = assessment;
    if (completed !== undefined) roadmap.completed = completed;

    const updatedRoadmap = await roadmap.save();
    res.json(updatedRoadmap);
  } catch (error) {
    logger.error('Error updating roadmap:', error);
    res.status(500).json({ message: 'Error updating roadmap' });
  }
};

// @desc    Update a module's completion status
// @route   PUT /api/roadmaps/:id/modules/:moduleId
// @access  Private
const updateModuleStatus = async (req, res) => {
  try {
    const { completed } = req.body;

    if (completed === undefined) {
      return res.status(400).json({ message: 'Completed status is required' });
    }

    const roadmap = await Roadmap.findOne({ _id: req.params.id, user: req.user.id });

    if (!roadmap) {
      return res.status(404).json({ message: 'Roadmap not found' });
    }

    // Find the module
    const moduleIndex = roadmap.modules.findIndex(m => m._id.toString() === req.params.moduleId);

    if (moduleIndex === -1) {
      return res.status(404).json({ message: 'Module not found' });
    }

    // Update the module's completion status
    roadmap.modules[moduleIndex].completed = completed;

    // Check if all modules are completed
    const allModulesCompleted = roadmap.modules.every(m => m.completed);
    if (allModulesCompleted) {
      roadmap.completed = true;
    } else {
      roadmap.completed = false;
    }

    const updatedRoadmap = await roadmap.save();
    res.json(updatedRoadmap);
  } catch (error) {
    logger.error('Error updating module status:', error);
    res.status(500).json({ message: 'Error updating module status' });
  }
};

// @desc    Delete a roadmap
// @route   DELETE /api/roadmaps/:id
// @access  Private
const deleteRoadmap = async (req, res) => {
  try {
    const roadmap = await Roadmap.findOne({ _id: req.params.id, user: req.user.id });

    if (!roadmap) {
      return res.status(404).json({ message: 'Roadmap not found' });
    }

    await roadmap.deleteOne();
    res.json({ message: 'Roadmap removed' });
  } catch (error) {
    logger.error('Error deleting roadmap:', error);
    res.status(500).json({ message: 'Error deleting roadmap' });
  }
};

module.exports = {
  createRoadmap,
  getRoadmaps,
  getRoadmapById,
  updateRoadmap,
  updateModuleStatus,
  deleteRoadmap,
};
