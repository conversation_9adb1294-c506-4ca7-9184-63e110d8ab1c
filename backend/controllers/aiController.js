const OpenAI = require('openai');
const logger = require('../config/logger');

// Initialize OpenAI
const openai = new OpenAI({
  apiKey: process.env.OPENAI_API_KEY,
});

// @desc    Generate reflective prompts
// @route   POST /api/ai/reflective-prompts
// @access  Private
const generateReflectivePrompts = async (req, res) => {
  try {
    const { noteContent } = req.body;

    if (!noteContent) {
      return res.status(400).json({ message: 'Note content is required' });
    }

    const response = await openai.chat.completions.create({
      model: "gpt-4",
      messages: [
        {
          role: "system",
          content: `You are an AI assistant that helps users reflect on their notes. 
          Generate 3 reflective questions based on the note content provided. 
          The questions should encourage deeper understanding and critical thinking.
          `
        },
        {
          role: "user",
          content: `Generate 3 reflective questions based on this note: "${noteContent}"`
        }
      ],
      max_tokens: 300,
    });

    // Extract the generated prompts from the response
    const generatedText = response.choices[0].message.content;

    // Parse the text to extract the questions
    const questions = generatedText.split('\n')
      .filter(line => line.trim().length > 0)
      .map(line => {
        // Remove numbering if present (e.g., "1. ", "2. ", etc.)
        return line.replace(/^\d+\.\s*/, '').trim();
      })
      .filter(question => question.endsWith('?'))
      .slice(0, 3); // Ensure we only get 3 questions

    // Format the response
    const formattedPrompts = questions.map(prompt => ({
      prompt,
      response: '',
    }));

    res.json({ reflectivePrompts: formattedPrompts });
  } catch (error) {
    logger.error('Error generating reflective prompts:', error);
    res.status(500).json({ message: 'Error generating reflective prompts' });
  }
};

// @desc    Generate simplified explanation (concept rebuilder)
// @route   POST /api/ai/concept-rebuild
// @access  Private
const generateConceptRebuild = async (req, res) => {
  try {
    const { noteContent } = req.body;

    if (!noteContent) {
      return res.status(400).json({ message: 'Note content is required' });
    }

    const response = await openai.chat.completions.create({
      model: "gpt-4",
      messages: [
        {
          role: "system",
          content: `You are an AI assistant that helps users understand complex concepts. 
          Provide a simplified explanation or analogy for the concept described in the note. 
          Use everyday language and relatable examples.`
        },
        {
          role: "user",
          content: `Provide a simplified explanation or analogy for this concept: "${noteContent}"`
        }
      ],
      max_tokens: 500,
    });

    const simplifiedExplanation = response.choices[0].message.content.trim();

    res.json({
      conceptRebuild: {
        simplifiedExplanation,
        userResponse: '',
      },
    });
  } catch (error) {
    logger.error('Error generating concept rebuild:', error);
    res.status(500).json({ message: 'Error generating simplified explanation' });
  }
};

// @desc    Generate Socratic questions
// @route   POST /api/ai/socratic-questions
// @access  Private
const generateSocraticQuestions = async (req, res) => {
  try {
    const { noteContent } = req.body;

    if (!noteContent) {
      return res.status(400).json({ message: 'Note content is required' });
    }

    const response = await openai.chat.completions.create({
      model: "gpt-4",
      messages: [
        {
          role: "system",
          content: "You are an AI assistant that helps users think critically about their notes. Generate 3 Socratic questions that challenge assumptions, explore implications, and encourage deeper analysis of the concepts in the note."
        },
        {
          role: "user",
          content: `Generate 3 Socratic questions based on this note: "${noteContent}"`
        }
      ],
      max_tokens: 300,
    });

    // Extract the generated questions from the response
    const generatedText = response.choices[0].message.content;

    // Parse the text to extract the questions
    const questions = generatedText.split('\n')
      .filter(line => line.trim().length > 0)
      .map(line => {
        // Remove numbering if present (e.g., "1. ", "2. ", etc.)
        return line.replace(/^\d+\.\s*/, '').trim();
      })
      .filter(question => question.endsWith('?'))
      .slice(0, 3); // Ensure we only get 3 questions

    // Format the response
    const formattedQuestions = questions.map(question => ({
      question,
      response: '',
    }));

    res.json({ socraticQuestions: formattedQuestions });
  } catch (error) {
    logger.error('Error generating Socratic questions:', error);
    res.status(500).json({ message: 'Error generating Socratic questions' });
  }
};

// @desc    Extract tags from note content
// @route   POST /api/ai/extract-tags
// @access  Private
const extractTags = async (req, res) => {
  try {
    const { noteContent } = req.body;

    if (!noteContent) {
      return res.status(400).json({ message: 'Note content is required' });
    }

    const response = await openai.chat.completions.create({
      model: "gpt-4",
      messages: [
        {
          role: "system",
          content: "You are an AI assistant that helps users organize their notes. Extract 3-5 relevant tags or keywords from the note content. Return only the tags as a comma-separated list, without any additional text."
        },
        {
          role: "user",
          content: `Extract 3-5 relevant tags from this note: "${noteContent}"`
        }
      ],
      max_tokens: 100,
    });

    const tagsText = response.choices[0].message.content.trim();

    // Parse the comma-separated list of tags
    const tags = tagsText
      .split(',')
      .map(tag => tag.trim())
      .filter(tag => tag.length > 0);

    res.json({ tags });
  } catch (error) {
    logger.error('Error extracting tags:', error);
    res.status(500).json({ message: 'Error extracting tags' });
  }
};

// @desc    Start a teach AI session
// @route   POST /api/ai/teach-ai/start
// @access  Private
const startTeachSession = async (req, res) => {
  try {
    const { noteContent } = req.body;

    if (!noteContent) {
      return res.status(400).json({ message: 'Note content is required' });
    }

    // Initialize an empty teach session
    const teachSession = {
      userExplanation: '',
      followUpQuestions: [],
      feedback: {
        strengths: [],
        improvements: [],
        suggestedRevision: '',
        confidenceScore: 0,
      },
      summary: '',
    };

    res.json({ teachSession });
  } catch (error) {
    logger.error('Error starting teach session:', error);
    res.status(500).json({ message: 'Error starting teach session' });
  }
};

// @desc    Submit user explanation and get first follow-up question
// @route   POST /api/ai/teach-ai/explain
// @access  Private
const submitExplanation = async (req, res) => {
  try {
    const { noteContent, userExplanation } = req.body;

    if (!noteContent || !userExplanation) {
      return res.status(400).json({ message: 'Note content and user explanation are required' });
    }

    // Generate the first follow-up question based on the user's explanation
    const response = await openai.chat.completions.create({
      model: 'gpt-3.5-turbo',
      messages: [
        {
          role: 'system',
          content: `You are an AI student who is learning from a human teacher. The human is explaining a concept to you from their notes.
          Your job is to ask a thoughtful follow-up question that will challenge their understanding and help them deepen their knowledge.

          The question should be casual and conversational with the human.

          Ask like a student who wants to learn from a teacher, not like you're evaluating the teacher.

          The question should be Socratic in nature and help the human improve their understanding through the act of teaching you.
          Format your response as a JSON object with a 'question' key containing a single question. For example: {"question": "How would this concept apply in [specific scenario]?"}`
        },
        {
          role: 'user',
          content: `Here is the original note content: "${noteContent}"\n\nHere is the human's explanation: "${userExplanation}"\n\nGenerate the first follow-up question to deepen their understanding.`
        }
      ],
      max_tokens: 500,
    });

    // Parse the response
    let question = '';
    try {
      const responseContent = JSON.parse(response.choices[0].message.content);
      question = responseContent.question || '';
    } catch (error) {
      // If JSON parsing fails, extract question using regex
      const content = response.choices[0].message.content;
      const questionMatch = content.match(/"([^"]+\?)"/g);
      if (questionMatch && questionMatch.length > 0) {
        question = questionMatch[0].replace(/"/g, '');
      } else {
        // Fallback: look for a line with a question mark
        const lines = content.split('\n');
        const questionLine = lines.find(line => line.includes('?'));
        if (questionLine) {
          question = questionLine.trim();
        } else {
          question = "Can you elaborate more on this concept?";
        }
      }
    }

    // Format the follow-up question
    const followUpQuestions = [{
      question,
      response: '',
    }];

    // Update the teach session
    const teachSession = {
      userExplanation,
      followUpQuestions,
      currentQuestionIndex: 0,
      questionHistory: [],
      feedback: {
        strengths: [],
        improvements: [],
        suggestedRevision: '',
        confidenceScore: 70,
      },
      summary: '',
    };

    res.json({ teachSession });
  } catch (error) {
    logger.error('Error submitting explanation:', error);
    res.status(500).json({ message: 'Error submitting explanation' });
  }
};

// @desc    Submit response to a question and get the next question
// @route   POST /api/ai/teach-ai/next-question
// @access  Private
const submitQuestionResponse = async (req, res) => {
  try {
    const { noteContent, userExplanation, currentQuestion, questionResponse, questionHistory = [] } = req.body;

    if (!noteContent || !userExplanation || !currentQuestion || !questionResponse) {
      return res.status(400).json({
        message: 'Note content, user explanation, current question, and question response are required'
      });
    }

    // Add the current question and response to the history
    const updatedHistory = [...questionHistory, {
      question: currentQuestion.question,
      response: questionResponse
    }];

    // Generate the next follow-up question based on the conversation so far
    const response = await openai.chat.completions.create({
      model: 'gpt-3.5-turbo',
      messages: [
        {
          role: 'system',
          content: `You are an AI student who is learning from a human teacher. The human is explaining a concept to you from their notes.
          Your job is to ask thoughtful follow-up questions that will challenge their understanding and help them deepen their knowledge.

          The questions should be casual and conversational with the human.

          Ask like a student who wants to learn from a teacher, not like you're evaluating the teacher.

          The question should be Socratic in nature and help the human improve their understanding through the act of teaching you.

          Based on their previous answers, ask a question that explores a different aspect of the concept or digs deeper into something they mentioned.

          Format your response as a JSON object with a 'question' key containing a single question. For example: {"question": "How would this concept apply in [specific scenario]?"}`
        },
        {
          role: 'user',
          content: `Original note content: "${noteContent}"

Teacher's initial explanation: "${userExplanation}"

Previous questions and answers:
${updatedHistory.map((qa, i) => `Question ${i + 1}: ${qa.question}\nAnswer: ${qa.response}`).join('\n\n')}

Generate the next follow-up question to deepen their understanding.`
        }
      ],
      max_tokens: 500,
    });

    // Parse the response
    let nextQuestion = '';
    try {
      const responseContent = JSON.parse(response.choices[0].message.content);
      nextQuestion = responseContent.question || '';
    } catch (error) {
      // If JSON parsing fails, extract question using regex
      const content = response.choices[0].message.content;
      const questionMatch = content.match(/"([^"]+\?)"/g);
      if (questionMatch && questionMatch.length > 0) {
        nextQuestion = questionMatch[0].replace(/"/g, '');
      } else {
        // Fallback: look for a line with a question mark
        const lines = content.split('\n');
        const questionLine = lines.find(line => line.includes('?'));
        if (questionLine) {
          nextQuestion = questionLine.trim();
        } else {
          nextQuestion = "Can you connect this concept to something else you've learned?";
        }
      }
    }

    // Format the next question
    const followUpQuestions = [{
      question: nextQuestion,
      response: '',
    }];

    // Update the teach session
    const teachSession = {
      userExplanation,
      followUpQuestions,
      currentQuestionIndex: 0,
      questionHistory: updatedHistory,
      feedback: {
        strengths: [],
        improvements: [],
        suggestedRevision: '',
        confidenceScore: 70,
      },
      summary: '',
    };

    res.json({ teachSession });
  } catch (error) {
    logger.error('Error generating next question:', error);
    res.status(500).json({ message: 'Error generating next question' });
  }
};

// @desc    Submit responses to follow-up questions and get feedback
// @route   POST /api/ai/teach-ai/feedback
// @access  Private
const generateFeedback = async (req, res) => {
  try {
    const { noteContent, userExplanation, questionHistory } = req.body;

    if (!noteContent || !userExplanation || !questionHistory || !questionHistory.length) {
      return res.status(400).json({ message: 'Note content, user explanation, and question history are required' });
    }

    // Format the question history for the prompt
    const questionsAndResponses = questionHistory
      .map((item, index) => `Question ${index + 1}: ${item.question}\nResponse: ${item.response}`)
      .join('\n\n');

    // Generate feedback based on the user's explanation and responses
    const response = await openai.chat.completions.create({
      model: 'gpt-3.5-turbo',
      messages: [
        {
          role: 'system',
          content: `You are an AI tutor evaluating how well a student understands a concept based on their explanation and responses to follow-up questions.

          Provide constructive feedback in the following JSON format:
          {
            "strengths": ["strength1", "strength2", "strength3"],
            "improvements": ["improvement1", "improvement2"],
            "suggestedRevision": "A concise, improved explanation that addresses the gaps",
            "confidenceScore": 85,
            "summary": "A brief summary of their overall understanding and key points to remember"
          }

          Be encouraging but honest. Focus on conceptual understanding rather than grammar or style.`
        },
        {
          role: 'user',
          content: `Original note content: "${noteContent}"\n\nStudent's explanation: "${userExplanation}"\n\nFollow-up questions and responses:\n${questionsAndResponses}\n\nProvide feedback on the student's understanding.`
        }
      ],
      max_tokens: 1000,
    });

    // Parse the feedback
    let feedback = {
      strengths: [],
      improvements: [],
      suggestedRevision: '',
      confidenceScore: 70,
      summary: ''
    };

    try {
      feedback = JSON.parse(response.choices[0].message.content);
    } catch (error) {
      // If JSON parsing fails, try to extract information from the text
      const content = response.choices[0].message.content;

      // Extract strengths
      const strengthsMatch = content.match(/strengths?[:\s]+(.*?)(?=improvements?[:\s]+|$)/is);
      if (strengthsMatch && strengthsMatch[1]) {
        feedback.strengths = strengthsMatch[1].split('\n')
          .filter(line => line.trim().length > 0)
          .map(line => line.replace(/^[\s\-\*•]+/, '').trim())
          .filter(line => line.length > 0)
          .slice(0, 3);
      }

      // Extract improvements
      const improvementsMatch = content.match(/improvements?[:\s]+(.*?)(?=suggested|summary|$)/is);
      if (improvementsMatch && improvementsMatch[1]) {
        feedback.improvements = improvementsMatch[1].split('\n')
          .filter(line => line.trim().length > 0)
          .map(line => line.replace(/^[\s\-\*•]+/, '').trim())
          .filter(line => line.length > 0)
          .slice(0, 3);
      }

      // Extract suggested revision
      const revisionMatch = content.match(/suggested[\s\w]+[:\s]+(.*?)(?=confidence|summary|$)/is);
      if (revisionMatch && revisionMatch[1]) {
        feedback.suggestedRevision = revisionMatch[1].trim();
      }

      // Extract confidence score
      const scoreMatch = content.match(/confidence[\s\w]+[:\s]+([0-9]+)/i);
      if (scoreMatch && scoreMatch[1]) {
        feedback.confidenceScore = parseInt(scoreMatch[1], 10);
      }

      // Extract summary
      const summaryMatch = content.match(/summary[:\s]+(.*?)(?=$)/is);
      if (summaryMatch && summaryMatch[1]) {
        feedback.summary = summaryMatch[1].trim();
      }
    }

    // Update the teach session
    const teachSession = {
      userExplanation,
      questionHistory,
      feedback: {
        strengths: feedback.strengths || [],
        improvements: feedback.improvements || [],
        suggestedRevision: feedback.suggestedRevision || '',
        confidenceScore: feedback.confidenceScore || 70,
      },
      summary: feedback.summary || '',
      completed: true,
    };

    res.json({ teachSession });
  } catch (error) {
    logger.error('Error generating feedback:', error);
    res.status(500).json({ message: 'Error generating feedback' });
  }
};

// @desc    Generate a learning roadmap for a concept
// @route   POST /api/ai/generate-roadmap
// @access  Private
const generateRoadmap = async (req, res) => {
  try {
    const { noteContent, userExplanation, questionHistory, feedback } = req.body;

    if (!noteContent || !feedback) {
      return res.status(400).json({ message: 'Note content and feedback are required' });
    }

    // Format the question history for the prompt
    const questionsAndResponses = questionHistory
      ? questionHistory
        .map((item, index) => `Question ${index + 1}: ${item.question}\nResponse: ${item.response}`)
        .join('\n\n')
      : '';

    // Generate a learning roadmap based on the user's understanding
    const response = await openai.chat.completions.create({
      model: 'gpt-3.5-turbo',
      messages: [
        {
          role: 'system',
          content: `You are an AI educational coach creating a personalized learning roadmap for a student who needs to improve their understanding of a concept.

          Based on their explanation, Q&A session, and feedback, create a comprehensive learning roadmap with the following structure:

          1. A title for the roadmap
          2. A brief overview of the concept and why it's important
          3. A list of identified knowledge gaps from their explanation
          4. A structured learning plan with:
             - 3-5 main learning modules/sections
             - Each module should have:
               * A clear title
               * Learning objectives
               * Recommended resources (books, videos, articles)
               * Practice exercises
               * A suggested timeframe (e.g., "Week 1: 3-4 hours")
          5. A final assessment method to test their improved understanding

          Format your response as a JSON object with the following structure:
          {
            "title": "Learning Roadmap: [Concept Name]",
            "overview": "A paragraph explaining the concept and its importance",
            "knowledgeGaps": ["Gap 1", "Gap 2", "Gap 3"],
            "modules": [
              {
                "title": "Module 1: [Title]",
                "objectives": ["Objective 1", "Objective 2"],
                "resources": ["Resource 1", "Resource 2"],
                "exercises": ["Exercise 1", "Exercise 2"],
                "timeframe": "Week 1: 3-4 hours"
              },
              // More modules...
            ],
            "assessment": "Description of how to assess their improved understanding"
          }`
        },
        {
          role: 'user',
          content: `Original note content: "${noteContent}"

Student's explanation: "${userExplanation || 'Not provided'}"

${questionsAndResponses ? `Follow-up questions and responses:
${questionsAndResponses}

` : ''}
Feedback on their understanding:
Strengths: ${feedback.strengths.join(', ')}
Areas for improvement: ${feedback.improvements.join(', ')}
Confidence score: ${feedback.confidenceScore}/100

Create a personalized learning roadmap to help this student improve their understanding of this concept.`
        }
      ],
      max_tokens: 2000,
    });

    // Parse the roadmap
    let roadmap = {};
    try {
      roadmap = JSON.parse(response.choices[0].message.content);
    } catch (error) {
      // If JSON parsing fails, return the raw text
      roadmap = {
        title: 'Learning Roadmap',
        rawContent: response.choices[0].message.content,
        parseError: true
      };
    }

    res.json({ roadmap });
  } catch (error) {
    logger.error('Error generating roadmap:', error);
    res.status(500).json({ message: 'Error generating roadmap' });
  }
};

module.exports = {
  generateReflectivePrompts,
  generateConceptRebuild,
  generateSocraticQuestions,
  extractTags,
  startTeachSession,
  submitExplanation,
  submitQuestionResponse,
  generateFeedback,
  generateRoadmap,
};
