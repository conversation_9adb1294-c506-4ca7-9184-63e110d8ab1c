const Note = require('../models/noteModel');
const logger = require('../config/logger');

// @desc    Get all notes for a user
// @route   GET /api/notes
// @access  Private
const getNotes = async (req, res) => {
  try {
    const notes = await Note.find({ user: req.user._id }).sort({ updatedAt: -1 });
    res.json(notes);
  } catch (error) {
    logger.error('Error getting notes:', error);
    res.status(500).json({ message: 'Server error' });
  }
};

// @desc    Get a single note
// @route   GET /api/notes/:id
// @access  Private
const getNoteById = async (req, res) => {
  try {
    const note = await Note.findById(req.params.id);

    // Check if note exists
    if (!note) {
      return res.status(404).json({ message: 'Note not found' });
    }

    // Check if user owns the note
    if (note.user.toString() !== req.user._id.toString()) {
      return res.status(401).json({ message: 'Not authorized' });
    }

    res.json(note);
  } catch (error) {
    logger.error(`Error getting note by ID ${req.params.id}:`, error);
    res.status(500).json({ message: 'Server error' });
  }
};

// @desc    Create a new note
// @route   POST /api/notes
// @access  Private
const createNote = async (req, res) => {
  try {
    const { title, content, tags } = req.body;

    const note = await Note.create({
      user: req.user._id,
      title,
      content,
      tags: tags || [],
    });

    res.status(201).json(note);
  } catch (error) {
    logger.error('Error creating note:', error);
    res.status(500).json({ message: 'Server error' });
  }
};

// @desc    Update a note
// @route   PUT /api/notes/:id
// @access  Private
const updateNote = async (req, res) => {
  try {
    const { title, content, tags } = req.body;

    const note = await Note.findById(req.params.id);

    // Check if note exists
    if (!note) {
      return res.status(404).json({ message: 'Note not found' });
    }

    // Check if user owns the note
    if (note.user.toString() !== req.user._id.toString()) {
      return res.status(401).json({ message: 'Not authorized' });
    }

    // Update note
    note.title = title || note.title;
    note.content = content || note.content;
    note.tags = tags || note.tags;

    const updatedNote = await note.save();
    res.json(updatedNote);
  } catch (error) {
    logger.error(`Error updating note ${req.params.id}:`, error);
    res.status(500).json({ message: 'Server error' });
  }
};

// @desc    Delete a note
// @route   DELETE /api/notes/:id
// @access  Private
const deleteNote = async (req, res) => {
  try {
    const note = await Note.findById(req.params.id);

    // Check if note exists
    if (!note) {
      return res.status(404).json({ message: 'Note not found' });
    }

    // Check if user owns the note
    if (note.user.toString() !== req.user._id.toString()) {
      return res.status(401).json({ message: 'Not authorized' });
    }

    await note.deleteOne();
    res.json({ message: 'Note removed' });
  } catch (error) {
    logger.error(`Error deleting note ${req.params.id}:`, error);
    res.status(500).json({ message: 'Server error' });
  }
};

// @desc    Update AI suggestions for a note
// @route   PUT /api/notes/:id/ai-suggestions
// @access  Private
const updateAiSuggestions = async (req, res) => {
  try {
    const { reflectivePrompts, conceptRebuild, socraticQuestions } = req.body;

    const note = await Note.findById(req.params.id);

    // Check if note exists
    if (!note) {
      return res.status(404).json({ message: 'Note not found' });
    }

    // Check if user owns the note
    if (note.user.toString() !== req.user._id.toString()) {
      return res.status(401).json({ message: 'Not authorized' });
    }

    // Update AI suggestions
    if (reflectivePrompts) {
      note.aiSuggestions.reflectivePrompts = reflectivePrompts;
    }

    if (conceptRebuild) {
      note.aiSuggestions.conceptRebuild = conceptRebuild;
    }

    if (socraticQuestions) {
      note.aiSuggestions.socraticQuestions = socraticQuestions;
    }

    const updatedNote = await note.save();
    res.json(updatedNote);
  } catch (error) {
    logger.error(`Error updating AI suggestions for note ${req.params.id}:`, error);
    res.status(500).json({ message: 'Server error' });
  }
};

module.exports = {
  getNotes,
  getNoteById,
  createNote,
  updateNote,
  deleteNote,
  updateAiSuggestions,
};
