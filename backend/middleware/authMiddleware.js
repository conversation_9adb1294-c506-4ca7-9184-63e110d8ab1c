const jwt = require('jsonwebtoken');
const User = require('../models/userModel');
const logger = require('../config/logger');

const protect = async (req, res, next) => {
  let token;

  // Check if token exists in headers
  if (
    req.headers.authorization &&
    req.headers.authorization.startsWith('Bearer')
  ) {
    try {
      // Get token from header
      token = req.headers.authorization.split(' ')[1];

      // Verify token
      const decoded = jwt.verify(token, process.env.JWT_SECRET);

      // Get user from the token (exclude password)
      req.user = await User.findById(decoded.id).select('-password');

      if (!req.user) {
        logger.warn(`User not found for token: ${decoded.id}`);
        return res.status(401).json({ message: 'User not found' });
      }

      return next();
    } catch (error) {
      logger.error('Authentication error: Token verification failed', error);
      return res.status(401).json({ message: 'Not authorized, token failed' });
    }
  }

  logger.warn(`Authentication attempt without token: ${req.originalUrl}`);
  return res.status(401).json({ message: 'Not authorized, no token' });
};

module.exports = { protect };
