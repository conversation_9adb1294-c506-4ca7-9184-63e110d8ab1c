const mongoose = require('mongoose');

const noteSchema = new mongoose.Schema(
  {
    user: {
      type: mongoose.Schema.Types.ObjectId,
      required: true,
      ref: 'User',
    },
    title: {
      type: String,
      required: [true, 'Please provide a title'],
      trim: true,
    },
    content: {
      type: String,
      required: [true, 'Please provide content'],
    },
    tags: [
      {
        type: String,
        trim: true,
      },
    ],
    aiSuggestions: {
      reflectivePrompts: [
        {
          prompt: String,
          response: String,
        },
      ],
      conceptRebuild: {
        simplifiedExplanation: String,
        userResponse: String,
      },
      socraticQuestions: [
        {
          question: String,
          response: String,
        },
      ],
      teachSession: {
        userExplanation: String,
        followUpQuestions: [
          {
            question: String,
            response: String,
          },
        ],
        feedback: {
          strengths: [String],
          improvements: [String],
          suggestedRevision: String,
          confidenceScore: Number,
        },
        summary: String,
      },
    },
    lastReviewed: {
      type: Date,
      default: Date.now,
    },
    nextReviewDate: {
      type: Date,
      default: function () {
        // Default to 1 day from now for first review
        const date = new Date();
        date.setDate(date.getDate() + 1);
        return date;
      },
    },
  },
  {
    timestamps: true,
  }
);

// Add text index for search functionality
noteSchema.index({ title: 'text', content: 'text', tags: 'text' });

const Note = mongoose.model('Note', noteSchema);

module.exports = Note;
