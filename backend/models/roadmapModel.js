const mongoose = require('mongoose');

const moduleSchema = new mongoose.Schema({
  title: {
    type: String,
    required: true,
  },
  objectives: [{
    type: String,
  }],
  resources: [{
    type: String,
  }],
  exercises: [{
    type: String,
  }],
  timeframe: {
    type: String,
  },
  completed: {
    type: Boolean,
    default: false,
  },
});

const roadmapSchema = new mongoose.Schema(
  {
    user: {
      type: mongoose.Schema.Types.ObjectId,
      required: true,
      ref: 'User',
    },
    relatedNote: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'Note',
    },
    title: {
      type: String,
      required: true,
    },
    overview: {
      type: String,
    },
    knowledgeGaps: [{
      type: String,
    }],
    modules: [moduleSchema],
    assessment: {
      type: String,
    },
    rawContent: {
      type: String,
    },
    parseError: {
      type: Boolean,
      default: false,
    },
    completed: {
      type: Boolean,
      default: false,
    },
  },
  {
    timestamps: true,
  }
);

const Roadmap = mongoose.model('Roadmap', roadmapSchema);

module.exports = Roadmap;
