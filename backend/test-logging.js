const logger = require('./config/logger');

// Test different log levels
logger.info('This is an info message');
logger.warn('This is a warning message');
logger.error('This is an error message');
logger.debug('This is a debug message');

// Test error logging with stack trace
try {
  throw new Error('Test error with stack trace');
} catch (error) {
  logger.error('Caught an error:', error);
}

console.log('Logging test complete. Check the logs directory for log files.');
